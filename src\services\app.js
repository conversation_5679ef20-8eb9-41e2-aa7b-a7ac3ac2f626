const uuid = require('uuid');
const jwt = require('jsonwebtoken');
const moment = require('moment');
const appDao = require('../daos/app');
const userDao = require('../daos/user');
const { APP_ROLE } = require('../constants');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { syncApp } = require('./sync');
const code = require('../errors/code');

const createApp = async ({ userId, name, expiresAt }) => {
  const user = await userDao.findUser({ _id: userId });
  if (!user) throw new CustomError(errorCodes.USER_NOT_FOUND);

  const { packageCode, packageExpiryDate } = user.apiPackage || {};
  const canCreateApp =
    packageCode &&
    (!packageExpiryDate ||
      packageExpiryDate < 0 ||
      moment().isBefore(moment(packageExpiryDate)));
  if (!canCreateApp) throw new CustomError(errorCodes.PACKAGE_EXPIRED);

  const secretKey = uuid.v4();
  let token = '';

  const expiresTime = expiresAt ? moment(expiresAt).endOf('day') : undefined;
  if (expiresTime) {
    const expiresIn = expiresTime.diff(moment(), 'seconds');
    token = await jwt.sign({}, secretKey, { expiresIn: `${expiresIn}s` });
  } else token = await jwt.sign({}, secretKey);

  const appId = uuid.v4();
  const app = await appDao.createApp({
    _id: appId,
    name,
    secretKey,
    token,
    expiresAt: expiresTime,
    members: [{ userId, role: APP_ROLE.ADMIN }],
  });

  const appDB = await appDao.findAppWithSecretKey({ _id: appId });

  appDao.saveAppInRedis(appDB);
  syncApp(app);

  const { secretKey: appSecretKey, ...appWithoutSecretKey } = app;

  return appWithoutSecretKey;
};

const updateApp = async (appId, updateFields) => {
  const app = await appDao.updateApp(appId, updateFields);
  appDao.updateAppInRedis(appId, updateFields);

  syncApp(app);

  const { secretKey: appSecretKey, ...appWithoutSecretKey } = app;

  return appWithoutSecretKey;
};

const getApps = async ({
  search,
  searchFields,
  dateField,
  query,
  offset,
  limit,
  fields,
  sort,
}) => {
  const queryFields = {};
  if (search) queryFields.search = search;
  if (offset) queryFields.offset = parseInt(offset, 10);
  if (limit) queryFields.limit = parseInt(limit, 10);
  if (fields) queryFields.fields = fields.split(',');
  if (sort) queryFields.sort = sort.split(',');
  if (query) queryFields.query = { ...queryFields.query, ...query };
  if (dateField) queryFields.dateField = dateField;
  if (searchFields) queryFields.searchFields = searchFields.split(',');

  const { apps, total } = await appDao.findApps(queryFields);

  return { apps, total };
};

const checkAppPermission = async (appId, appToken) => {
  const app = await appDao.findAppWithSecretKey({ _id: appId });
  const { secretKey, token } = app;

  if (!secretKey) throw new CustomError(code.UNAUTHORIZED);
  if (appToken !== token) throw new CustomError(code.UNAUTHORIZED);
};

module.exports = { createApp, updateApp, getApps, checkAppPermission };
