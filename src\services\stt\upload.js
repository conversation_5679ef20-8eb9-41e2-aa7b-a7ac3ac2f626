const fs = require('fs');
const uuid = require('uuid');
const axios = require('axios');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');

const { isValidUrl } = require('../../utils/string');

const {
  S3_ACCESS_KEY_ID,
  S3_SECRET_ACCESS_KEY,
  S3_ENDPOINT,
  S3_REGION,
  STT_S3_ACCESS_KEY_ID,
  STT_S3_SECRET_ACCESS_KEY,
  STT_S3_REGION,
} = require('../../configs');

const uploadBufferFileToS3Minio = async ({ bucket, fileKey, fileBuffer }) => {
  const s3Client = new S3Client({
    endpoint: S3_ENDPOINT,
    region: S3_REGION,
    credentials: {
      accessKeyId: S3_ACCESS_KEY_ID,
      secretAccessKey: S3_SECRET_ACCESS_KEY,
    },
    forcePathStyle: true,
  });
  logger.info(`Uploading to ${S3_ENDPOINT}/${bucket}/${fileKey}`);
  await s3Client.send(
    new PutObjectCommand({
      Bucket: bucket,
      Key: fileKey,
      Body: fileBuffer,
    }),
  );
  return `${S3_ENDPOINT}/${bucket}/${fileKey}`;
};

const uploadBufferFileToS3Aws = async ({ bucket, fileKey, fileBuffer }) => {
  const s3Client = new S3Client({
    region: S3_REGION,
    credentials: {
      accessKeyId: STT_S3_ACCESS_KEY_ID,
      secretAccessKey: STT_S3_SECRET_ACCESS_KEY,
    },
  });
  logger.info(
    `Uploading to https://${bucket}.${STT_S3_REGION}.amazonaws.com/${fileKey}`,
  );
  await s3Client.send(
    new PutObjectCommand({
      Bucket: bucket,
      Key: fileKey,
      Body: fileBuffer,
    }),
  );
  // https://vbee-audio.s3.ap-southeast-1.amazonaws.com/infra-icons/kdata.jpeg
  return `https://${bucket}.s3.${STT_S3_REGION}.amazonaws.com/${fileKey}`;
};

const getUploadDir = () => {
  const dir = 'uploads';
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
  }
  return dir;
};

const getUploadFileName = (file) => {
  const extArray = file.mimetype.split('/');
  const extension = extArray[extArray.length - 1];
  return `${file.fieldname}-${Date.now()}-${uuid.v4()}.${extension}`;
};

const downloadFile = async (url, path) => {
  if (isValidUrl(url)) {
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
    });
    const buffer = Buffer.from(response.data);
    if (path) {
      await fs.writeFileSync(path, buffer);
    }
    return buffer;
  }
  throw new Error('Invalid url');
};

const uploadToUrl = async (url, filePath, contentType) => {
  // ADVISE: in NodeJS runtime, try to use async version for better performance. Wrap readFile to our FileHelper
  const buffer = await fs.readFileSync(filePath);
  await axios.put(url, buffer, {
    headers: {
      'Content-Type': contentType,
    },
  });
};

module.exports = {
  getUploadDir,
  getUploadFileName,
  uploadS3: S3_ENDPOINT ? uploadBufferFileToS3Minio : uploadBufferFileToS3Aws,
  downloadFile,
  uploadToUrl,
};
