const { getAccessToken } = require('./iam');
const callApi = require('../utils/callApi');

const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { IAM_URL, SYNC_ACCOUNT_FROM_IAM_INTERVAL } = require('../configs');

const getBannedAccountsFromIAM = async (params) => {
  const accessToken = await getAccessToken();

  try {
    const response = await callApi({
      method: 'GET',
      url: `${IAM_URL}/api/v1/banned-accounts`,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      params,
    });
    return response;
  } catch (error) {
    logger.error(error, { ctx: 'syncAccountToService' });
    throw new CustomError(
      errorCodes.BAD_REQUEST,
      `Failed to get banned accounts from IAM: ${error?.message}`,
    );
  }
};

const syncBannedAccounts = async () => {
  const bannedAccounts = [];
  const limit = 500;
  let offset = 0;
  let hasNext = true;

  while (hasNext) {
    const { result } = await getBannedAccountsFromIAM({ offset, limit });
    const { bannedAccounts: bannedAccountsChunk, total: totalChunk } =
      result || {};
    bannedAccounts.push(...bannedAccountsChunk);
    offset += limit;
    hasNext = bannedAccounts.length < totalChunk;
  }

  global.BANNED_ACCOUNTS = bannedAccounts.map(({ type, value }) => ({
    type,
    value,
  }));
};

const syncBannedAccountsToCache = async () => {
  try {
    await syncBannedAccounts();
  } catch (error) {
    logger.error(error, { ctx: 'syncBannedAccountsToCache' });
  } finally {
    setTimeout(syncBannedAccountsToCache, SYNC_ACCOUNT_FROM_IAM_INTERVAL);
  }
};

module.exports = { syncBannedAccountsToCache };
