const errorReportService = require('../services/errorReport');
const errorReportDao = require('../daos/errorReport');

const createErrorReport = async (req, res) => {
  const { requestId } = req.params;
  const { description } = req.body;
  const { user } = req;

  await errorReportService.createErrorReport(requestId, user, description);
  return res.send({});
};

const getErrorReports = async (req, res) => {
  const { search, searchFields, fields, offset, limit, sort } = req.query;
  const query = {};
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (searchFields) query.searchFields = searchFields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  Object.keys(req.query)
    .filter(
      (q) => ['search', 'fields', 'offset', 'limit', 'sort'].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { errorReports, total } = await errorReportDao.findErrorReports(query);
  return res.send({ errorReports, total });
};

const getErrorReport = async (req, res) => {
  const { errorReportId } = req.params;

  const errorReport = await errorReportService.getErrorReport(errorReportId);

  return res.send(errorReport);
};

module.exports = { createErrorReport, getErrorReports, getErrorReport };
