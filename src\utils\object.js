/* eslint-disable no-shadow */
const convertObjectIdToString = (obj) => {
  if (typeof obj !== 'object') {
    return obj;
  }

  // typeof null = object
  if (obj === null) {
    return null;
  }

  // Check if obj is Mongoose Object
  if (obj._doc) {
    return convertObjectIdToString(obj.toJSON());
  }

  // Check if obj is ObjectId
  if (obj._bsontype === 'ObjectID' || obj._bsontype === 'ObjectId') {
    return obj.toString();
  }

  Object.keys(obj).forEach((key) => {
    // eslint-disable-next-line no-param-reassign
    obj[key] = convertObjectIdToString(obj[key]);
  });

  if (Array.isArray(obj)) return obj;
  if (Object.prototype.toString.call(obj) === '[object Date]') return obj;

  return obj;
};

const normalizeObject = (obj) => convertObjectIdToString(obj);

const splitArray = (arr, size) =>
  arr.reduce((result, value, i) => {
    if (i % size === 0) result.push([]);
    result[result.length - 1].push(value);
    return result;
  }, []);

const isEmptyObject = (obj) => {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
};

const removeUndefined = (obj) => {
  // eslint-disable-next-line guard-for-in
  for (const key in obj) {
    const isObject = typeof obj[key] === 'object' && !Array.isArray(obj[key]);
    const isNil = obj[key] === undefined;
    const isDate = isObject && obj[key] instanceof Date;
    const isNull = obj[key] === null;

    if (isNil) {
      delete obj[key];
    } else if (isObject) {
      if (isDate) {
        // eslint-disable-next-line no-continue
        continue;
        // eslint-disable-next-line no-continue
      } else if (isNull) continue;

      removeUndefined(obj[key]);
      const isEmptyObject = Object.keys(obj[key]).length === 0;
      if (isEmptyObject) delete obj[key];
    }
  }
  return obj;
};

module.exports = {
  normalizeObject,
  splitArray,
  isEmptyObject,
  removeUndefined,
};
