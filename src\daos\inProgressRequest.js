const InProgressRequest = require('../models/inProgressRequest');

const findInProgressRequestById = async (id) => {
  const inProgressRequest = await InProgressRequest.findById(id);
  return inProgressRequest;
};

const createInProgressRequest = async (requestId, createdAt) => {
  const inProgressRequestExist = await InProgressRequest.findById(requestId);
  if (inProgressRequestExist) return;

  await InProgressRequest.create({ _id: requestId, createdAt });
};

const updateProcessingAt = async (id, processingAt) => {
  const inProgressRequest = await InProgressRequest.findByIdAndUpdate(
    id,
    { processingAt },
    { new: true },
  );
  return inProgressRequest;
};

const deleteInProgressRequest = async (id) => {
  await InProgressRequest.findByIdAndDelete(id);
};

module.exports = {
  findInProgressRequestById,
  createInProgressRequest,
  updateProcessingAt,
  deleteInProgressRequest,
};
