const inprogressRequestDao = require('../daos/inProgressRequest');
const requestDao = require('../daos/request');
const { delay } = require('../utils/delay');

const updateProcessingAt = async (requestId, processingAt) => {
  let maxRetry = 5;
  let inProgressRequest;
  await requestDao.updateRequestById(requestId, { processingAt });

  await requestDao.updateRequestByIdInRedis(requestId, { processingAt });

  do {
    await delay(1000);
    inProgressRequest = await inprogressRequestDao.findInProgressRequestById(
      requestId,
    );
    maxRetry -= 1;
  } while (!inProgressRequest && maxRetry > 0);

  if (inProgressRequest)
    await inprogressRequestDao.updateProcessingAt(requestId, processingAt);
  else
    logger.warn(`Inprogress Request not found ${requestId}`, {
      ctx: 'UpdateProcessingAt',
    });
};

module.exports = { updateProcessingAt };
