const redis = require('redis');

const { REDIS_URI } = require('../../configs');

const redisClient = redis.createClient({ url: REDIS_URI });

redisClient.on('error', (err) => {
  logger.error(`Connect error to Redis: ${REDIS_URI}`, {
    ctx: 'Redis',
    stack: err.stack,
  });
});

redisClient
  .connect()
  .then(() =>
    logger.info(`Connected to Redis: ${REDIS_URI}`, { ctx: 'Redis' }),
  );

const setKeyWithTtl = async (key, value, ttl) => {
  await redisClient.set(key, value, {
    EX: ttl,
  });
};

const setKeyIfNotExists = async (key, value) => {
  const res = await redisClient.setNX(key, value);
  return res;
};

module.exports = { redisClient, setKeyWithTtl, setKeyIfNotExists };
