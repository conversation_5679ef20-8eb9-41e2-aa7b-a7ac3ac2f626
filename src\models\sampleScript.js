const mongoose = require('mongoose');
const { AUDIO_TYPE } = require('../constants');

const sampleScriptSchema = new mongoose.Schema(
  {
    title: String,
    text: String,
    icon: String,
    audios: [
      {
        voiceCode: String,
        speed: Number,
        bitrate: Number,
        sampleRate: String,
        backgroundMusic: {
          name: String,
          link: String,
          volume: Number,
        },
        audioType: { type: String, enum: Object.values(AUDIO_TYPE) },
        audioLink: String,
      },
    ],
    rank: Number,
    isActive: { type: Boolean, default: true },
    isVietnamese: Boolean,
    isDefault: { type: Boolean, default: false },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);
module.exports = mongoose.model(
  'SampleScript',
  sampleScriptSchema,
  'sample_scripts',
);
