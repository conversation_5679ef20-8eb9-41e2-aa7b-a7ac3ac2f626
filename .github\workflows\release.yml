name: Release

on:
  release:
    types: [published]

jobs:
  uat-ci:
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.VBEE_UAT_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VBEE_UAT_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VBEE_UAT_AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Google Auth
        id: google-auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.VBEE_PROD_GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Google Artifact Registry
        run: |
          gcloud auth configure-docker ${{ vars.GAR_LOCATION }}-docker.pkg.dev --quiet

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ github.repository }}
          GAR_REGISTRY: ${{ vars.GAR_LOCATION }}-docker.pkg.dev
          GAR_PROJECT_ID: ${{ vars.VBEE_PROD_GCP_PROJECT_ID }}
          GAR_REPOSITORY: ${{ github.repository }}
          IMAGE_TAG: ${{ github.event.release.tag_name }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          REPO_NAME=${{ github.repository }}
          docker build --build-arg NPM_TOKEN=$NPM_TOKEN -t $REPO_NAME .

          docker tag $REPO_NAME $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

          docker tag $REPO_NAME $GAR_REGISTRY/$GAR_PROJECT_ID/$GAR_REPOSITORY:$IMAGE_TAG
          docker push $GAR_REGISTRY/$GAR_PROJECT_ID/$GAR_REPOSITORY:$IMAGE_TAG

  prod-ci:
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.VBEE_PROD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VBEE_PROD_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VBEE_PROD_AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ github.repository }}
          IMAGE_TAG: ${{ github.event.release.tag_name }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG --build-arg NPM_TOKEN=$NPM_TOKEN .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
