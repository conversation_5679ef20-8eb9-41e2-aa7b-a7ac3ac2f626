const axios = require('axios');
const { google } = require('googleapis');

const getAccessToken = (key) => {
  const SCOPES = ['https://www.googleapis.com/auth/drive'];
  return new Promise((resolve, reject) => {
    const jwtClient = new google.auth.JWT(
      key.client_email,
      null,
      key.private_key,
      SCOPES,
      null,
    );
    jwtClient.authorize((err, tokens) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(tokens.access_token);
    });
  });
};

const getValues = async ({ sheetId, sheetName, sheetRange }) => {
  const key = require('../configs/credentials.json');
  const accessToken = await getAccessToken(key);

  const { data } = await axios({
    url: `https://sheets.googleapis.com/v4/spreadsheets/${sheetId}/values/${encodeURI(
      sheetName,
    )}!${sheetRange}`,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return data.values;
};

module.exports = {
  getValues,
};
