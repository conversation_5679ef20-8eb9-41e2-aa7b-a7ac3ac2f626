const { getFeatureValue } = require('../services/growthbook');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const {
  verifyRecaptchaToken,
  verifyEnterpriseRecaptchaToken,
} = require('../services/recaptcha');
const asyncMiddleware = require('./async');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { RECAPTCHA_TYPE, PLATFORM_TYPE } = require('../constants/recaptcha');
const { checkAppDevice } = require('../services/device');
const userDao = require('../daos/user');

const featureKeysByRoute = {
  '/synthesis': FEATURE_KEYS.RECAPTCHA_TTS_TYPE,
};

const checkBrowserErrorException = async (userId) => {
  const user = await userDao.findUser({ _id: userId });
  if (!user) throw new CustomError(errorCodes.USER_NOT_FOUND);
  const { numOfConsecutiveBrowserErrorReq = 0 } = user.recaptcha || {};

  switch (numOfConsecutiveBrowserErrorReq) {
    case 0:
    case 1:
      return true;

    default: {
      const currentSecond = new Date().getSeconds();
      return currentSecond % 2 === 0;
    }
  }
};

const checkRecaptchaV3 = async (req, res, next) => {
  const { userId } = req.user;
  await userDao.increaseTotalReqRecaptcha(userId);

  const clientRecaptchaToken = req.headers['recaptcha-token'];

  try {
    await verifyRecaptchaToken(clientRecaptchaToken, req.user);

    await userDao.resetConsecutiveBrowserErrorReqRecaptcha(userId);
  } catch (error) {
    const { message, errorCode } = error;
    const isBrowserError = errorCode === errorCodes.BROWSER_ERROR;

    await userDao.increaseTotalInvalidReqRecaptcha(userId, isBrowserError);

    if (isBrowserError) {
      const hasException = await checkBrowserErrorException(userId);
      if (hasException) return next();
    }

    throw new CustomError(errorCodes.BAD_REQUEST, message);
  }

  return next();
};

const checkRecaptchaEnterprise = async (req, res, next) => {
  const { userId } = req.user;
  const userAgent = req.headers['user-agent'];
  const appPlatform = req.headers.platform;
  const isAppDevice = checkAppDevice(userAgent);

  await userDao.increaseTotalReqRecaptcha(userId);

  const clientRecaptchaToken = req.headers['recaptcha-token'];
  const platform = isAppDevice ? appPlatform : PLATFORM_TYPE.WEBSITE;

  try {
    await verifyEnterpriseRecaptchaToken(
      clientRecaptchaToken,
      req.user,
      platform,
    );

    await userDao.resetConsecutiveBrowserErrorReqRecaptcha(userId);
  } catch (error) {
    const { message, errorCode } = error;
    const isBrowserError = errorCode === errorCodes.BROWSER_ERROR;

    await userDao.increaseTotalInvalidReqRecaptcha(userId, isBrowserError);

    if (isBrowserError) {
      const hasException = await checkBrowserErrorException(userId);
      if (hasException) return next();
    }

    throw new CustomError(errorCodes.BAD_REQUEST, message);
  }

  return next();
};

const checkRecaptcha = async (req, res, next) => {
  const { userId, email } = req.user;
  const userAgent = req.headers['user-agent'];
  const appVersion = req.headers['app-version'];
  const isAppDevice = checkAppDevice(userAgent);

  const featureKey = featureKeysByRoute[req.path];
  const randomNumber = (Date.now() % 100) + 1;
  const recaptchaType = featureKey
    ? getFeatureValue(featureKey, {
        userId,
        email,
        isAppDevice,
        appVersion,
        randomNumber,
      })
    : '';

  switch (recaptchaType) {
    case RECAPTCHA_TYPE.V3:
      await checkRecaptchaV3(req, res, next);
      break;
    case RECAPTCHA_TYPE.ENTERPRISE:
      await checkRecaptchaEnterprise(req, res, next);
      break;
    default:
      next();
      break;
  }
};

module.exports = {
  checkRecaptcha: asyncMiddleware(checkRecaptcha),
};
