const { redisClient } = require('./redis');
const {
  REDIS_KEY_PREFIX,
  KAFKA_TOPIC,
  REDIS_KEY_TTL,
  REQUEST_TYPE,
  REQUEST_STATUS,
  PACKAGE_FEATURE,
  TTS_PROCESSING_STEPS,
} = require('../constants');
const requestDao = require('../daos/request');
const userDao = require('../daos/user');
const clientPauseDao = require('../daos/clientPause');
const { processText, getMsClientPause } = require('./preprocessing');
const { sendMessage, sendMessages } = require('./kafka/producer');
const { SYNTHESIS_BY_GATEWAY } = require('../configs');
const {
  saveStepProcessingTime,
  getProcessingTime,
} = require('./processingTime');
const { incrTotalProcessingRequests } = require('./metrics');
const { updateProcessingAt } = require('./inprogressRequest');
const { findRequestById } = require('../daos/request');
const { getPackageUsageOptions } = require('./package');

const getText = async ({
  requestType,
  text,
  voice,
  version,
  paragraphBreak,
  hasClientPause,
}) => {
  if (requestType === REQUEST_TYPE.DUBBING) return text;

  const normalizeText = await processText({
    text,
    voiceProvider: voice.provider,
    voiceLanguage: voice.languageCode,
    ttsCoreVersion: version,
    paragraphBreak,
    hasClientPause,
  });

  return normalizeText;
};

const sendPendingRequestToKafka = async ({ requestId, userId }) => {
  try {
    const request = await requestDao.findRequestById(requestId);
    const processingTime = await getProcessingTime(requestId);
    const { pushToQueueAt } = processingTime;
    if (request.type === REQUEST_TYPE.API_CACHING) {
      request.sentenceKeys = [`${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`];
      await requestId('./request').createRequestInRedis(request);
    }
    if (!request) throw new Error('Request is not exists');

    let paragraphBreak;
    let sentenceBreak;
    let majorBreak;
    let mediumBreak;
    let hasClientPause;

    if (userId) {
      const user = await userDao.findUser({ _id: userId });
      const studioUsageOptions = await getPackageUsageOptions({
        userId,
        packageCode: user.packageCode,
        userUsageOptions: user,
      });
      if (!user) throw new Error('User is not exists');

      hasClientPause = studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.CLIENT_PAUSE,
      );
      if (hasClientPause) {
        const clientPause = await clientPauseDao.findClientPause(userId);
        ({ paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
          clientPause || {});
      }
    }

    const kafkaTopic = KAFKA_TOPIC.SENTENCE_TOKENIZATION_REQUEST;

    const {
      text,
      numberOfSentences,
      voice = {},
      awsZoneFunctions,
      awsZoneSynthesis,
      type,
      version,
    } = request;
    const {
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      srtFunction,
    } = awsZoneFunctions;

    // multi voice
    if (numberOfSentences > 0) {
      const sentences = await requestDao.getDetailSentencesByRequestId(
        requestId,
      );

      saveStepProcessingTime({
        requestId,
        step: TTS_PROCESSING_STEPS.QUEUE,
        startTime: pushToQueueAt,
        additionalFields: { startSentenceTokenizerAt: Date.now() },
      });

      const messages = await Promise.all(
        sentences.map(async (sentence, index) => {
          const { text: sentenceText, voice: sentenceVoice } = sentence;

          const normalizeText = await processText({
            text: sentenceText,
            voiceProvider: sentenceVoice.provider,
            voiceLanguage: sentenceVoice.languageCode,
            ttsCoreVersion: request.version,
            paragraphBreak,
            hasClientPause,
          });

          return {
            value: {
              requestId,
              index,
              text: normalizeText,
              voice: sentenceVoice,
              ttsCoreVersion: request.version,
              awsZoneSynthesis,
              sentenceTokenizerFunction,
              newSentenceTokenizerFunction,
              clientPause: getMsClientPause({
                paragraphBreak,
                sentenceBreak,
                majorBreak,
                mediumBreak,
              }),
            },
          };
        }),
      );

      await sendMessages(kafkaTopic, messages);
      return;
    }

    const normalizeText = await getText({
      requestType: type,
      text,
      voice,
      version,
      paragraphBreak,
      hasClientPause,
    });

    const data = {
      requestId,
      index: 0,
      text: normalizeText,
      voice,
      ttsCoreVersion: request.version,
      clientPause: getMsClientPause({
        paragraphBreak,
        sentenceBreak,
        majorBreak,
        mediumBreak,
      }),
      srtFunction,
      awsZoneSynthesis,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
    };

    saveStepProcessingTime({
      requestId,
      step: TTS_PROCESSING_STEPS.QUEUE,
      startTime: pushToQueueAt,
      additionalFields: { startSentenceTokenizerAt: Date.now() },
    });

    await sendMessage(kafkaTopic, { value: data });
  } catch (error) {
    logger.error(error, { ctx: 'ProcessRequestByCcr', requestId, userId });
    require('./synthesis').handleSentenceTokenizationFailureResponse(
      requestId,
      error.message,
    );
  }
};

const runSentenceTokenizerQueue = async (userId, requestType) => {
  const pendingReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;

  const numOfPendingReq = await redisClient.lLen(pendingReqKey);
  if (numOfPendingReq === 0) {
    logger.warn(`No pending request`, { ctx: 'ProcessRequestByCcr', userId });
    return;
  }

  const requestId = await redisClient.rPop(pendingReqKey);
  incrTotalProcessingRequests();
  await updateProcessingAt(requestId, new Date());
  logger.info('Pop pending request from queue', {
    ctx: 'ProcessRequestByCcr',
    requestId,
    userId,
  });

  if (!requestId) {
    logger.warn(`No pending request`, { ctx: 'ProcessRequestByCcr', userId });
    return;
  }

  const request = await findRequestById(requestId);
  if (request?.status === REQUEST_STATUS.FAILURE) {
    logger.warn(`Request is already processed as failure`, {
      ctx: 'ProcessRequestByCcr',
      requestId,
      userId,
    });
    decrPendAndInprRequests(userId, requestId, request.type);
    return;
  }

  if (SYNTHESIS_BY_GATEWAY)
    require('./ttsProcessing').callApiSynthesis(requestId);
  else sendPendingRequestToKafka({ requestId, userId });
};

const pushRequestToQueue = async ({ userId, requestId, ccr, requestType }) => {
  const numOfPendAndInprReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

  const pendingReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;

  const pushQueueStatus = await redisClient.lPush(pendingReqKey, requestId);
  logger.info('Push pending requests to queue', {
    ctx: 'ProcessRequestByCcr',
    requestId,
    userId,
  });

  if (pushQueueStatus) {
    const numOfPendAndInprReq = await redisClient.incr(numOfPendAndInprReqKey);
    if (ccr >= 0 && numOfPendAndInprReq > ccr) {
      logger.warn(
        `Queue ${numOfPendAndInprReqKey} is max ccr: ${numOfPendAndInprReq}`,
        { ctx: 'ProcessRequestByCcr', userId, requestId },
      );
      return pushQueueStatus;
    }

    runSentenceTokenizerQueue(userId, requestType);
  }

  return pushQueueStatus;
};

const decrPendAndInprRequests = async (userId, requestId, requestType) => {
  const numPendAndInprReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

  const numOfPendAndInprReq = await redisClient.decr(numPendAndInprReqKey);
  logger.info(`Decrease ${numPendAndInprReqKey}: ${numOfPendAndInprReq}`, {
    ctx: 'ProcessRequestByCcr',
    userId,
    requestId,
  });

  if (numOfPendAndInprReq < 0) {
    const incrNumber = Math.abs(numOfPendAndInprReq);
    await redisClient.incr(numPendAndInprReqKey, incrNumber);
  }

  runSentenceTokenizerQueue(userId, requestType);
};

const processQueueWhenRequestFailure = async ({
  userId,
  requestId,
  requestType,
}) => {
  const failureKey = `${REDIS_KEY_PREFIX.FAILURE_REQUEST}_${requestId}`;

  const numOfFailure = await redisClient.incr(failureKey);
  redisClient.expire(failureKey, REDIS_KEY_TTL.FAILURE_REQUEST);

  if (numOfFailure > 1) return;

  decrPendAndInprRequests(userId, requestId, requestType);
};

const processQueueWhenRequestSuccess = async ({
  userId,
  requestId,
  requestType,
}) => {
  const successKey = `${REDIS_KEY_PREFIX.SUCCESS_REQUEST}_${requestId}`;

  const numOfSuccess = await redisClient.incr(successKey);
  redisClient.expire(successKey, REDIS_KEY_TTL.SUCCESS_REQUEST);

  if (numOfSuccess > 1) return;

  if (requestType !== REQUEST_TYPE.API_CACHING)
    decrPendAndInprRequests(userId, requestId, requestType);
};

const processRemovePendingReqByType = async ({ requestType, userId }) => {
  if (!Array.isArray(requestType) || requestType.length === 0) return;

  await Promise.all(
    requestType.map((type) =>
      removePendingRequests({ requestType: type, userId }),
    ),
  );
};

const removePendingRequests = async ({ requestType, userId }) => {
  const pendingReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;

  const pendingAndInprReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

  await redisClient.del(pendingReqKey);
  await redisClient.del(pendingAndInprReqKey);
};

module.exports = {
  pushRequestToQueue,
  decrPendAndInprRequests,
  processQueueWhenRequestFailure,
  processQueueWhenRequestSuccess,
  sendPendingRequestToKafka,
  processRemovePendingReqByType,
};
