const scanAndHandleByBatch = async ({
  dateField,
  dateRange,
  batchSize = 1000,
  getQueryFn,
  countDocumentsFn,
  getDocumentsFn,
  handleDocumentsFn,
}) => {
  logger.info(`Start scanning and handling documents by batch`);

  const { start, end } = dateRange;
  const query = getQueryFn({ dateField, dateRange });
  const totalDocuments = await countDocumentsFn(query);
  logger.info(`Total ${totalDocuments} documents`);

  let currTimeStamp = end;
  let handledDocs = 0;
  let handledDocIds = [];

  do {
    const q = getQueryFn({
      dateField,
      dateRange: { start, end: currTimeStamp },
      excludeDocIds: handledDocIds,
    });
    const documents = await getDocumentsFn(q, {
      limit: batchSize,
      sortField: dateField,
    });
    if (!documents.length) break;

    // Handle documents
    await handleDocumentsFn(documents);

    // Update progress
    currTimeStamp = documents[documents.length - 1][dateField];
    handledDocs += documents.length;
    handledDocIds = documents.map((doc) => doc._id);
    const percent = ((handledDocs / totalDocuments) * 100).toFixed(2);
    logger.debug(
      `Progress: ${percent}% - docs: ${handledDocs} - currTimestamp: ${currTimeStamp}`,
    );
    if (handledDocs >= totalDocuments) break;
  } while (true);

  logger.info('Completed scanning and handling documents by batch');
};

module.exports = { scanAndHandleByBatch };
