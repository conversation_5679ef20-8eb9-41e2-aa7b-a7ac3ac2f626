const parse = require('csv-parser');
const fs = require('fs');
const BackgroundMusic = require('../../models/backgroundMusic');

const initBackgroundMusic = (csvPath) => {
  const csvData = [];
  const prefixUri =
    'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/background-music';
  fs.createReadStream(csvPath)
    .pipe(parse({ delimiter: ',' }))
    .on('data', (csvRow) => {
      csvData.push(csvRow);
    })
    .on('end', async () => {
      const musics = csvData.map((item) => ({
        link: `${prefixUri}/${item.name}.mp3`,
        name: item.name,
      }));
      await BackgroundMusic.insertMany(musics);
      logger.info(`Created ${musics.length} background music records.`);
    });
};

initBackgroundMusic(`${__dirname}/export.csv`);
