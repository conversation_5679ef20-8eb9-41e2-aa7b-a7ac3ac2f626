const VoiceCloning = require('../models/voiceCloning');
const daoUtils = require('./utils');
const { findLanguages } = require('./language');
const { VOICE_OWNERSHIP } = require('../constants/voice');
const { VOICE_STATUS } = require('../constants/voiceCloning');

const createVoiceCloningVoice = async ({
  userId,
  code,
  name,
  image,
  gender,
  locale,
  province,
  status,
  languageCode,
  provider,
  squareImage,
  roundImage,
  sampleRates,
  defaultSampleRate,
  global,
  isSample,
}) => {
  const voiceCloning = await VoiceCloning.updateOne(
    { code },
    {
      userId,
      code,
      name,
      image,
      gender,
      locale,
      province,
      status,
      languageCode,
      provider,
      squareImage,
      roundImage,
      sampleRates,
      defaultSampleRate,
      global,
      isSample,
    },
    { upsert: true, new: true },
  );
  return voiceCloning;
};

const findVoiceCloningByCode = async (code) => {
  const voice = await VoiceCloning.findOne({ code }).lean();

  return voice;
};

const findVoiceCloningVoices = async (query = {}) => {
  const {
    search,
    searchFields = ['name'],
    query: queryField,
    offset,
    limit,
    fields,
    sort = ['rank_asc'],
  } = query;

  let dataQuery = {};
  if (queryField) {
    const { gender, languageCode, features, level, ...otherQuery } = queryField;
    dataQuery = { ...otherQuery };
    if (gender) dataQuery.gender = { $in: gender.split(',') };
    if (languageCode) dataQuery.languageCode = { $in: languageCode.split(',') };
    if (level) dataQuery.level = { $in: level.split(',') };
    if (features)
      dataQuery.features = { $elemMatch: { $in: features.split(',') } };
  }

  const { documents: voices, total } = await daoUtils.find(VoiceCloning, {
    search,
    searchFields,
    query: dataQuery,
    offset,
    limit,
    fields,
    sort,
  });

  if (!LANGUAGES) {
    const { languages } = await findLanguages();
    global.LANGUAGES = languages;
  }

  const detailVoices = voices.map((voice) => {
    const language = LANGUAGES.find((item) => item.code === voice.languageCode);
    return { ...voice, language };
  });

  return { voices: detailVoices, total };
};

const findVoiceCloningByOwnerShip = async ({ voiceOwnership, userId }) => {
  const query =
    voiceOwnership === VOICE_OWNERSHIP.COMMUNITY
      ? {
          status: VOICE_STATUS.PUBLIC,
          $or: [
            { discardAt: { $exists: false } },
            { discardAt: { $gt: new Date() } },
          ],
        }
      : { userId, status: { $ne: VOICE_STATUS.REVIEWING } };

  const result = await findVoiceCloningVoices({ query });
  return result;
};

const updateVoiceCloningVoiceStatus = async ({ code, status }) => {
  const voice = await findVoiceCloningByCode(code);
  if (!voice) return;

  await VoiceCloning.findOneAndUpdate({ code }, { status });
};

const updateClonedVoiceInfo = async (code, updateData) => {
  await VoiceCloning.findOneAndUpdate({ code }, updateData);
};

module.exports = {
  createVoiceCloningVoice,
  findVoiceCloningByCode,
  findVoiceCloningVoices,
  findVoiceCloningByOwnerShip,
  updateVoiceCloningVoiceStatus,
  updateClonedVoiceInfo,
};
