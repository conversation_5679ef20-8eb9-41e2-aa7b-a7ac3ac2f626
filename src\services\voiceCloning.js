const moment = require('moment');
const CustomError = require('../errors/CustomError');
const errorCode = require('../errors/code');
const voiceCloningDao = require('../daos/voiceCloning');
const callApi = require('../utils/callApi');
const {
  LANGUAGE_CODE,
  VC_SAMPLE_RATES,
  VC_DEFAULT_SAMPLE_RATE,
  VOICE_STATUS,
} = require('../constants/voiceCloning');
const { TTS_GATE_URL, AI_VOICE_TOKEN, AI_VOICE_APP_ID } = require('../configs');
const { VOICE_PROVIDER } = require('../constants');

const createVoiceInTTSGate = async (ttsPayload = {}) => {
  const res = await callApi({
    headers: { authorization: `Bearer ${AI_VOICE_TOKEN}` },
    url: `${TTS_GATE_URL}/api/v1/voices`,
    method: 'POST',
    data: { appId: AI_VOICE_APP_ID, ...ttsPayload },
  });

  if (res.status !== 1)
    throw new CustomError(
      errorCode.CALL_API_CREATE_VOICE_IN_TTS_GATE_FAILED,
      res.message,
    );
};

const createVoiceCloningVoice = async ({
  userId,
  code,
  name,
  gender,
  avatar,
  locale,
  province,
  status,
}) => {
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);

  const createVoiceData = {
    code,
    name,
    image: avatar,
    gender,
    locale,
    province,
    status,
    languageCode: LANGUAGE_CODE.VI,
    provider: VOICE_PROVIDER.VBEE_VOICE_CLONING,
    squareImage: avatar,
    roundImage: avatar,
    sampleRates: VC_SAMPLE_RATES,
    defaultSampleRate: VC_DEFAULT_SAMPLE_RATE,
  };

  if (!voice) await createVoiceInTTSGate(createVoiceData);

  const voiceCloningVoice = await voiceCloningDao.createVoiceCloningVoice({
    userId,
    ...createVoiceData,
  });

  return voiceCloningVoice;
};

const getClonedVoiceByCode = async ({
  code,
  userId,
  isValidateVoice = true,
  canUsePublicVoiceCloning,
}) => {
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  const { status, userId: voiceOwnerId, discardAt } = voice;
  if (status === VOICE_STATUS.PUBLIC) {
    if (!canUsePublicVoiceCloning)
      throw new CustomError(errorCode.INVALID_VOICE_CODE);

    if (discardAt && moment().isAfter(discardAt) && isValidateVoice)
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

    return voice;
  }
  if (
    status === VOICE_STATUS.PRIVATE &&
    userId !== voiceOwnerId &&
    isValidateVoice
  )
    throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

  return voice;
};

const updateClonedVoice = async (updateData) => {
  const { userId, code, name, avatar, status, demo } = updateData;
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  if (voice.userId !== userId)
    throw new CustomError(errorCode.INVALID_VOICE_CODE);

  const updateVoiceData = {
    name,
    image: avatar,
    squareImage: avatar,
    roundImage: avatar,
  };
  if (status) updateVoiceData.status = status;
  if (demo) updateVoiceData.demo = demo;

  await voiceCloningDao.updateClonedVoiceInfo(code, updateVoiceData);
};

module.exports = {
  createVoiceCloningVoice,
  getClonedVoiceByCode,
  updateClonedVoice,
};
