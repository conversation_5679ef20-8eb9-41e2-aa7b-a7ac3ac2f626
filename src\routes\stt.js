const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const sttApiController = require('../controllers/stt');
const { authAPI } = require('../middlewares/auth');
const { checkBlock<PERSON>pi } = require('../middlewares/checkBlock');
const uploadMiddleware = require('../middlewares/upload');
const { apiSttValidate } = require('../validations/stt');

/* eslint-disable prettier/prettier */
router.post('/stt', authAPI, checkBlockApi,  uploadMiddleware.uploadSingle('fileContent'), apiSttValidate, asyncMiddleware(sttApiController.apiStt));
router.post('/stt/callback',  asyncMiddleware(sttApiController.apiCallbackResponse));
router.get('/stt/:requestId', asyncMiddleware(sttApiController.getSttRequest));

/* eslint-disable prettier/prettier */

module.exports = router;
