const uuid = require('uuid');
require('dotenv').config();
const {
  TTS_STATUS,
  BREAK_LINE,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  REQUEST_TYPE,
} = require('../constants');
const { SILENCE_BREAK_LINE_AUDIO } = require('../configs');
const Tts = require('../models/tts');
const {
  getSearchQuery,
  getSortQuery,
  getSelectQuery,
  getDateQuery,
} = require('./utils/util');
const { redisClient, setKeyWithTtl } = require('../services/redis');

const checkSentenceExistInCache = async (requestId, sentenceIndex) => {
  const sentenceStatusKey = `${REDIS_KEY_PREFIX.SENTENCE_TOKENIZATION_STATUS}_${requestId}_${sentenceIndex}`;
  const sentenceStatus = await redisClient.get(sentenceStatusKey);

  return !!sentenceStatus;
};

const saveTtsInRedis = async ({ requestId, index, ttsRequests }) => {
  ttsRequests = await Promise.all(
    ttsRequests.map(async (tts) => {
      const ttsId = uuid.v4();
      tts._id = ttsId;

      const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
      const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

      setKeyWithTtl(ttsKey, JSON.stringify(tts), ttsKeyTtl);
      return tts;
    }),
  );
  const ttsRequestIds = ttsRequests.map((tts) => {
    return {
      index,
      ttsId: tts._id,
      subIndex: tts.subIndex,
    };
  });

  const sentenceIndexKey = `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${index}`;
  await setKeyWithTtl(
    sentenceIndexKey,
    JSON.stringify(ttsRequestIds),
    REDIS_KEY_TTL.SENTENCE,
  );

  return ttsRequests;
};

const getStudioSentences = ({
  requestId,
  index,
  voiceCode,
  sentences,
  breakLineAudioUrl,
  awsZoneSynthesis,
}) => {
  const ttsRequests = sentences
    .filter((text) => text === BREAK_LINE || text.trim().length > 0)
    .map((text, subIndex) => ({
      requestId,
      index,
      subIndex,
      text,
      voiceCode,
      status: text === BREAK_LINE ? TTS_STATUS.SUCCESS : TTS_STATUS.IN_PROGRESS,
      audioLink: text === BREAK_LINE ? breakLineAudioUrl : undefined,
      silence: text === BREAK_LINE,
      awsZoneSynthesis,
    }));
  return ttsRequests;
};

const getDubbingSentences = ({
  requestId,
  index,
  voiceCode,
  sentences,
  breakLineAudioUrl,
  awsZoneSynthesis,
}) => {
  const ttsRequests = sentences
    .filter(
      (sentence) =>
        sentence.content === BREAK_LINE || sentence.content.trim().length > 0,
    )
    .map((sentence, subIndex) => {
      const { content, start, end } = sentence;
      return {
        requestId,
        index,
        subIndex,
        text: content,
        start,
        end,
        voiceCode,
        status:
          content === BREAK_LINE ? TTS_STATUS.SUCCESS : TTS_STATUS.IN_PROGRESS,
        audioLink: content === BREAK_LINE ? breakLineAudioUrl : undefined,
        silence: content === BREAK_LINE,
        awsZoneSynthesis,
      };
    });

  return ttsRequests;
};

const getTtsRequests = ({
  requestId,
  index,
  voiceCode,
  sentences,
  type,
  breakLineAudioUrl,
  awsZoneSynthesis,
}) => {
  const getTtsRequestsFunc =
    type === REQUEST_TYPE.DUBBING ? getDubbingSentences : getStudioSentences;
  return getTtsRequestsFunc({
    requestId,
    index,
    voiceCode,
    sentences,
    breakLineAudioUrl,
    awsZoneSynthesis,
  });
};

const saveSentences = async ({
  requestId,
  index,
  sentences,
  type,
  voiceCode,
  audioType,
  sampleRate = '16000',
  awsZoneSynthesis,
}) => {
  const isSaved = await checkSentenceExistInCache(requestId, index);
  if (isSaved) return null;

  const sentenceStatusKey = `${REDIS_KEY_PREFIX.SENTENCE_TOKENIZATION_STATUS}_${requestId}_${index}`;
  // const numOfSentenceCompletedKey = `${REDIS_KEY_PREFIX.COMPLETED_SENTENCE_TOKENIZATION}_${requestId}`;
  await redisClient.incr(sentenceStatusKey);
  // const numOfSentenceCompleted = await redisClient.incr(
  //   numOfSentenceCompletedKey,
  // );
  await redisClient.expire(
    sentenceStatusKey,
    REDIS_KEY_TTL.SENTENCE_TOKENIZATION_STATUS,
  );
  // await redisClient.expire(
  //   numOfSentenceCompletedKey,
  //   REDIS_KEY_TTL.COMPLETED_SENTENCE_TOKENIZATION,
  // );

  const breakLineAudioUrl = `${SILENCE_BREAK_LINE_AUDIO}-${sampleRate}Hz.${audioType}`;

  const ttsRequests = getTtsRequests({
    requestId,
    index,
    voiceCode,
    sentences,
    type,
    breakLineAudioUrl,
    awsZoneSynthesis,
  });

  const ttsIdRequests = await saveTtsInRedis({ requestId, index, ttsRequests });

  return { ttsIdRequests };
};

const saveAudio = async ({
  requestId,
  index,
  subIndex,
  audioLink,
  t2aDuration,
  synthesisDuration,
}) => {
  await Tts.updateOne(
    { requestId, index, subIndex },
    { audioLink, status: TTS_STATUS.SUCCESS, t2aDuration, synthesisDuration },
  );
};

const saveAudioInRedis = async ({
  requestId,
  ttsId,
  audioLink,
  audioName,
  t2aDuration,
  synthesisDuration,
  phrases,
}) => {
  const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
  const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

  const tts = await redisClient.get(ttsKey);
  if (!tts) return;

  const ttsObj = JSON.parse(tts);
  const newTts = {
    ...ttsObj,
    audioName,
    audioLink,
    status: TTS_STATUS.SUCCESS,
    t2aDuration,
    synthesisDuration,
    phrases,
  };
  await setKeyWithTtl(ttsKey, JSON.stringify(newTts), ttsKeyTtl);
};

const updateFailureTTS = async ({ requestId, index, subIndex, error }) => {
  await Tts.updateOne(
    { requestId, index, subIndex },
    { status: TTS_STATUS.FAILURE, error },
  );
};

const updateFailureTTSInRedis = async ({ requestId, ttsId, error }) => {
  const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
  const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

  const tts = await redisClient.get(ttsKey);
  if (!tts) return null;

  const ttsObj = JSON.parse(tts);
  const newTts = { ...ttsObj, status: TTS_STATUS.FAILURE, error };

  await setKeyWithTtl(ttsKey, JSON.stringify(newTts), ttsKeyTtl);

  return newTts;
};

const checkCompletedIndex = async (requestId, index) => {
  const ttsRequest = await Tts.findOne({
    requestId,
    index,
    status: { $ne: TTS_STATUS.SUCCESS },
  });
  return !ttsRequest;
};

const checkCompletedIndexInRedis = async (requestId, index) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId, index);

  const isNotCompleted = ttsList.some(
    (tts) => tts.status !== TTS_STATUS.SUCCESS,
  );
  return !isNotCompleted;
};

const getAudios = async (requestId) => {
  const tts = await Tts.aggregate([
    { $match: { requestId } },
    { $project: { index: 1, subIndex: 1, audioLink: 1, phrases: 1 } },
    { $sort: { index: 1, subIndex: 1 } },
  ]);

  const audios = tts.reduce((acc, curr) => {
    const { audioLink, phrases = [] } = curr;
    if (audioLink) return [...acc, audioLink];

    const sortPhrases = phrases.sort((a, b) => a.index - b.index);
    const audioLinks = sortPhrases.map((phrase) => phrase.audioLink);
    return [...acc, ...audioLinks];
  }, []);

  return audios;
};

const getAudiosInRedis = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const sortTtsList = ttsList.sort((a, b) => {
    return a.index - b.index || a.subIndex - b.subIndex;
  });

  const audios = sortTtsList.reduce((acc, curr) => {
    const { audioLink, phrases = [] } = curr;
    if (audioLink) return [...acc, audioLink];

    const sortPhrases = phrases.sort((a, b) => a.index - b.index);
    const audioLinks = sortPhrases.map((phrase) => phrase.audioLink);
    return [...acc, ...audioLinks];
  }, []);

  return audios;
};

const getSynthesisTime = async (requestId) => {
  const requests = await Tts.find(
    { requestId },
    { t2aDuration: 1, synthesisDuration: 1 },
  ).lean();

  const t2aDurations = requests.reduce(
    (prev, curr) => (curr.t2aDuration ? [...prev, curr.t2aDuration] : prev),
    [],
  );
  const synthesisDurations = requests.reduce(
    (prev, curr) =>
      curr.synthesisDuration ? [...prev, curr.synthesisDuration] : prev,
    [],
  );

  return { t2aDurations, synthesisDurations };
};

const getSynthesisTimeInRedis = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const t2aDurations = ttsList.reduce(
    (prev, curr) => (curr.t2aDuration ? [...prev, curr.t2aDuration] : prev),
    [],
  );
  const synthesisDurations = ttsList.reduce(
    (prev, curr) =>
      curr.synthesisDuration ? [...prev, curr.synthesisDuration] : prev,
    [],
  );

  return { t2aDurations, synthesisDurations };
};

const getTotalTtsByRequestId = async (requestId) => {
  const totalTtsRequest = await Tts.countDocuments({ requestId });
  return totalTtsRequest;
};

const getTotalTtsByRequestIdInRedis = async (requestId) => {
  const request = await require('./request').findRequestByIdInRedis(requestId);
  const totalTtsRequest = request?.ttsIds?.length || 0;
  return totalTtsRequest;
};

const getTotalSuccessTtsByRequestId = async (requestId) => {
  const totalTtsRequest = await Tts.countDocuments({
    requestId,
    status: TTS_STATUS.SUCCESS,
  });

  return totalTtsRequest;
};

const getTotalSuccessTtsByRequestIdInRedis = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const totalSuccessTts = ttsList.filter(
    (tts) => tts.status === TTS_STATUS.SUCCESS,
  ).length;

  return totalSuccessTts;
};

const deleteTTSByRequestId = async (requestId) => {
  await Tts.deleteMany({ requestId });
};

const findTts = async ({
  search,
  searchFields = [],
  dateField = 'createdAt',
  query,
  offset,
  limit,
  fields,
  sort,
}) => {
  const s = getSearchQuery(Tts, searchFields, search);

  // eslint-disable-next-line prefer-const
  let { startDate, endDate, ...dataQuery } = query || {};

  if (startDate || endDate) {
    const dateQuery = getDateQuery(dateField, query.startDate, query.endDate);
    dataQuery = { ...dataQuery, ...dateQuery };
  }

  const total = await Tts.countDocuments(
    search ? { $or: s, ...dataQuery } : dataQuery,
  );

  const pipeline = [
    { $match: search ? { $or: s, ...dataQuery } : dataQuery },
    {
      $lookup: {
        from: 'voices',
        localField: 'voiceCode',
        foreignField: 'code',
        as: 'voice',
      },
    },
    { $unwind: '$voice' },
  ];

  const filterPipe = [{ $skip: offset || 0 }, { $limit: limit || null }];

  if (sort) filterPipe.sort = getSortQuery(sort);
  if (fields) filterPipe.fields = getSelectQuery(fields);

  const tts = await Tts.aggregate([...pipeline, ...filterPipe]);

  return { tts, total };
};

const countRealTts = async (requestId) => {
  const total = await Tts.countDocuments({
    requestId,
    silence: { $ne: true },
  });
  return total;
};

const countRealTtsInRedis = async (requestId) => {
  const key = `${REDIS_KEY_PREFIX.TOTAL_TTS}_${requestId}`;
  let totalTtsRequest = await redisClient.get(key);
  if (totalTtsRequest) return Number(totalTtsRequest);

  const realTtsList = (await getTtsFromRequestIdInRedis(requestId)).filter(
    (tts) => !tts.silence,
  );
  totalTtsRequest = realTtsList.length || 1;

  await setKeyWithTtl(key, totalTtsRequest, REDIS_KEY_TTL.SENTENCE);
  return totalTtsRequest;
};

const updateCachePhrases = async ({ requestId, index, subIndex, phrases }) => {
  await Tts.updateOne(
    { requestId, index, subIndex },
    {
      phrases,
      index,
      subIndex,
      requestId,
      status: TTS_STATUS.SUCCESS,
      t2aDuration: 0,
      synthesisDuration: 0,
    },
  );
};

const updateCachePhrasesInRedis = async ({
  requestId,
  ttsId,
  index,
  subIndex,
  phrases,
}) => {
  try {
    const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
    const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

    const tts = await redisClient.get(ttsKey);
    if (!tts) return;

    const ttsObj = JSON.parse(tts);
    const newTts = {
      ...ttsObj,
      phrases,
      requestId,
      index,
      subIndex,
      status: TTS_STATUS.SUCCESS,
      t2aDuration: 0,
      synthesisDuration: 0,
    };

    await setKeyWithTtl(ttsKey, JSON.stringify(newTts), ttsKeyTtl);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
  }
};

const getTtsFromRequestIdInRedis = async (requestId, index) => {
  try {
    const request = await require('./request').findRequestByIdInRedis(
      requestId,
    );
    let { ttsIds = [] } = request || {};
    if (index) ttsIds = ttsIds.filter((tts) => tts.index === index);
    const ttsKeys = ttsIds.map(
      (tts) => `${REDIS_KEY_PREFIX.TTS}_${requestId}_${tts.ttsId}`,
    );
    if (!ttsKeys.length) return [];

    const ttsList = await redisClient.mGet(ttsKeys);
    if (!ttsList?.length) return [];

    const ttsObjList = ttsList.map((tts) => JSON.parse(tts));
    return ttsObjList;
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return [];
  }
};

const migrateTtsFromRedisToDB = async (requestId) => {
  try {
    const ttsList = await getTtsFromRequestIdInRedis(requestId);
    const migratedTtsKey = `${REDIS_KEY_PREFIX.MIGRATED_TTS}_${requestId}`;
    const checkMigrate = await redisClient.incr(migratedTtsKey);

    if (checkMigrate > 1) return;

    await Tts.insertMany(ttsList);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
  }
};

module.exports = {
  saveSentences,
  saveAudio,
  saveAudioInRedis,
  updateFailureTTS,
  updateFailureTTSInRedis,
  checkCompletedIndex,
  checkCompletedIndexInRedis,
  getAudios,
  getAudiosInRedis,
  getSynthesisTime,
  getSynthesisTimeInRedis,
  getTotalTtsByRequestId,
  getTotalTtsByRequestIdInRedis,
  getTotalSuccessTtsByRequestId,
  getTotalSuccessTtsByRequestIdInRedis,
  deleteTTSByRequestId,
  findTts,
  countRealTts,
  countRealTtsInRedis,
  updateCachePhrases,
  updateCachePhrasesInRedis,
  getTtsFromRequestIdInRedis,
  migrateTtsFromRedisToDB,
};
