const backgroundMusicDao = require('../daos/backgroundMusic');
const { isValidHttpUrl } = require('../utils/checkValid');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const createBackgroundMusic = async (userId, name, link) => {
  if (!isValidHttpUrl(link))
    throw new CustomError(errorCodes.BAD_REQUEST, 'Link invalid');

  let bgMusicName = name;
  if (!bgMusicName) {
    const { pathname } = new URL(link);
    const replaceName = pathname.split('/')[1];
    bgMusicName = replaceName;
  }

  const backgroundMusic = await backgroundMusicDao.createBackgroundMusic({
    userId,
    name: bgMusicName,
    link,
  });
  return backgroundMusic;
};

const getLinkBackgroundMusicByRegion = ({
  backgroundMusic,
  bucketName,
  region,
}) => {
  const parts = backgroundMusic.split('.com');
  const newS3Url = `https://${bucketName}.s3-${region}.amazonaws.com${parts[1]}`;

  return newS3Url;
};

module.exports = { createBackgroundMusic, getLinkBackgroundMusicByRegion };
