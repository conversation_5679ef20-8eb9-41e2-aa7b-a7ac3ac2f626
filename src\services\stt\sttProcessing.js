const fs = require('fs');
const {
  STT_APP_ID,
  STT_GATE_URL,
  STT_TOKEN,
  STT_S3_BUCKET,
  AIV_STT_CALLBACK_URL,
} = require('../../configs');
const callApi = require('../../utils/callApi');
const userDao = require('../../daos/user');
const requestDao = require('../../daos/request');
const { SYNC_SECONDS_EVENT } = require('../../constants');
const { isValidUrl } = require('../../utils/string');
const uploadService = require('./upload');
const errorCodes = require('../../errors/code');
const CustomError = require('../../errors/CustomError');
const { ASR_FILE_DURATION_LIMIT } = require('../../constants/stt');

const callProcessStt = async (request) => {
  try {
    const { fileContent, sampleRateHertz, sampleSizeByte, fileUrl, channel } =
      request?.stt || {};
    const data = {
      callbackUrl: `${AIV_STT_CALLBACK_URL}/api/v1/stt/callback`,
      responseType: request.responseType,
      fileContent,
      sampleRateHertz,
      sampleSizeByte,
      channel,
      fileUrl,
      clientRequestId: request.requestId,
    };

    const response = await callApi({
      headers: { authorization: `Bearer ${STT_TOKEN}`, 'App-ID': STT_APP_ID },
      url: `${STT_GATE_URL}/api/v1/stt`,
      method: 'POST',
      data,
      timeout: 60 * 1000,
    });

    if (!response.status) throw new Error(response.message);

    return response;
  } catch (error) {
    logger.error('Call process stt error', { error, ctx: 'CallProcessStt' });
    return { status: 0, errorMessage: 'Call process stt error' };
  }
};

const handleUpdateSttSeconds = async ({ userId, seconds }) => {
  await userDao.updateUserById(userId, {
    $inc: { 'stt.remainingSeconds': seconds },
  });
};

const updateSttSeconds = async ({ event, userId, requestId, seconds }) => {
  const user = await userDao.findUser({ _id: userId });
  if (!user) {
    logger.error('User not found', { userId, ctx: 'UpdateBySeconds' });
    return;
  }

  const request = await requestDao.findRequestById(requestId);
  if (!request) {
    logger.error('Request not found', {
      requestId,
      ctx: 'UpdateBySeconds',
    });
    return;
  }

  switch (event) {
    case SYNC_SECONDS_EVENT.SPEND: {
      if (request.paid) break;

      await handleUpdateSttSeconds({ userId, seconds: -seconds });
      await requestDao.updateRequestById(requestId, { paid: true });
      break;
    }

    case SYNC_SECONDS_EVENT.REFUND: {
      if (request.refund) break;

      await handleUpdateSttSeconds({ userId, seconds: request.seconds || 0 });
      await requestDao.updateRequestById(requestId, { refund: true });
      break;
    }

    default:
      break;
  }
};

const validateFile = async ({ filePath, audioBuffer }) => {
  if (!fs.existsSync(filePath)) {
    throw new CustomError(errorCodes.BAD_REQUEST, 'File not found');
  }
  const { WaveFile } = require('wavefile');

  const wav = new WaveFile(audioBuffer);
  const configAudio = {
    sampleRateHertz: wav.fmt.sampleRate,
    sampleSizeByte: wav.fmt.bitsPerSample / 8,
    channel: wav.fmt.numChannels,
  };

  const fileStats = fs.statSync(filePath);

  const audioDuration = fileStats.size / wav.fmt.byteRate / wav.fmt.numChannels;

  if (audioDuration * 1000 > ASR_FILE_DURATION_LIMIT) {
    throw new CustomError(
      errorCodes.BAD_REQUEST,
      'Audio duration is too long, must be less than 2 minutes',
    );
  }

  fs.unlink(filePath, (err) => {
    if (err) {
      logger.error(`Failed to delete file: ${filePath}`);
    }
  });
  return { audioDuration, configAudio };
};

// eslint-disable-next-line consistent-return
const validateRecognizeUploadFileRequest = async (req) => {
  const { file, fileUrl } = Object.assign(req, req.body);

  // console.log('fileContent', file);
  const isToProcessFile = file && file.path && fs.existsSync(file.path);
  const isToProcessUrl = isValidUrl(fileUrl);

  if (!isToProcessFile && !isToProcessUrl) {
    throw new CustomError(
      errorCodes.BAD_REQUEST,
      'File or url is required to process',
    );
  }

  let filePath;
  let audioUrl;
  let audioBuffer;

  if (isToProcessUrl) {
    filePath = `${uploadService.getUploadDir()}/${Date.now()}.wav`;
    audioUrl = fileUrl;
    await uploadService.downloadFile(fileUrl, filePath);
    // ADVISE: in NodeJS runtime, try to use async version for better performance. Wrap readFile to our FileHelper
    audioBuffer = fs.readFileSync(filePath);

    const { audioDuration, configAudio } = await validateFile({
      filePath,
      audioBuffer,
      req,
    });
    return { audioUrl, audioDuration, configAudio };
  }

  if (isToProcessFile) {
    filePath = file?.path;
    // ADVISE: in NodeJS runtime, try to use async version for better performance. Wrap readFile to our FileHelper
    audioBuffer = fs.readFileSync(filePath);

    const { audioDuration, configAudio } = await validateFile({
      filePath,
      audioBuffer,
      req,
    });

    const fileFullName = filePath.split('/').pop();
    const [fileName, fileExtension] = fileFullName.split('.');
    const bucketFilePath = `stt-upload/${fileName}.${fileExtension}`;
    audioUrl = await uploadService.uploadS3({
      bucket: STT_S3_BUCKET,
      fileKey: bucketFilePath,
      fileBuffer: audioBuffer,
    });
    return { audioUrl, audioDuration, configAudio };
  }
};

module.exports = {
  callProcessStt,
  updateSttSeconds,
  validateRecognizeUploadFileRequest,
};
