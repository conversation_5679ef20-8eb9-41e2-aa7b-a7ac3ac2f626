const { redisClient } = require('../redis');

const decrInProgressRequests = async (metric) => {
  const current = await metric.get();
  const currentVal = current?.values[0]?.value || 0;
  if (currentVal <= 0) return;
  metric.dec(1);
};

const incrCounterOnRedis = async (redisKey) => {
  try {
    await redisClient.incr(redisKey);
  } catch (err) {
    logger.error(`Incr ${redisKey} on Redis failed`, {
      ctx: 'IncrCounterOnRedis',
      stack: err.stack,
    });
  }
};

const decrCounterOnRedis = async (redisKey) => {
  try {
    const counter = await redisClient.decr(redisKey);
    if (counter < 0) {
      await redisClient.set(redisKey, 0);
    }
  } catch (err) {
    logger.error(`Decr ${redisKey} on Redis failed`, {
      ctx: 'DecrCounterOnRedis',
      stack: err.stack,
    });
  }
};

module.exports = {
  decrInProgressRequests,
  incrCounterOnRedis,
  decrCounterOnRedis,
};
