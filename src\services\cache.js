const md5 = require('md5');
const {
  KAFKA_TOPIC,
  REDIS_KEY_TTL,
  TTS_CORE_VERSION,
  REQUEST_TYPE,
} = require('../constants');
const { findDictionary } = require('../daos/dictionary');
const { updateCachePhrasesInRedis } = require('../daos/tts');
const { sendMessage } = require('./kafka/producer');
const { redisClient, setKeyWithTtl } = require('./redis');

const getCacheSentenceKey = (
  text,
  { voiceCode, audioType, speed, bitrate, sampleRate, awsZoneSynthesis },
) =>
  md5(
    `${text}${voiceCode}${audioType}${speed}${bitrate}${sampleRate}${awsZoneSynthesis}`,
  );

const getSynthesisSentences = async ({
  sentences = [],
  request,
  dictionary,
  version,
  awsZoneSynthesis,
}) => {
  const {
    _id: requestId,
    voice,
    audioType,
    speed,
    bitrate,
    sampleRate,
    type,
  } = request;

  if (type === REQUEST_TYPE.DUBBING) return sentences;
  const noCacheSentences = await sentences.reduce(async (accPromise, curr) => {
    const acc = await accPromise;
    const { text, subIndex, index, _id } = curr;
    const synthesisObject = { text, subIndex, index, _id };

    if (version !== TTS_CORE_VERSION.NEW) return [...acc, synthesisObject];

    const usedDictionary = dictionary?.words?.some((dic) =>
      text.includes(dic.word),
    );
    if (usedDictionary) return [...acc, synthesisObject];

    const cacheSentenceKey = await getCacheSentenceKey(text, {
      voiceCode: voice.code,
      audioType,
      speed,
      bitrate,
      sampleRate,
      awsZoneSynthesis,
    });
    const cacheAudioString = await redisClient.get(cacheSentenceKey);
    if (!cacheAudioString) return [...acc, synthesisObject];

    const cacheAudios = JSON.parse(cacheAudioString);
    const phrases = cacheAudios.map((audioLink, phraseIndex) => ({
      index: phraseIndex,
      audioLink,
      a2aDuration: 0,
      cache: true,
    }));

    updateCachePhrasesInRedis({
      requestId,
      ttsId: _id,
      index,
      subIndex,
      phrases,
    });

    const synthesisSuccessMessage = {
      value: {
        requestId,
        index,
        subIndex,
        ttsId: _id,
        text,
        voiceCode: voice.code,
        phrases,
        t2aDuration: 0,
        synthesisDuration: 0,
        cache: true,
      },
    };
    sendMessage(KAFKA_TOPIC.SYNTHESIS_SUCCESS, synthesisSuccessMessage);

    return acc;
  }, Promise.resolve([]));

  return noCacheSentences;
};

const handleCacheSentence = async ({
  userId,
  text,
  phrases,
  voiceCode,
  audioType,
  speed,
  bitrate,
  sampleRate,
  awsZoneSynthesis,
}) => {
  const dictionary = await findDictionary(userId);
  const usedDictionary = dictionary?.word?.some((dic) =>
    text.includes(dic.word),
  );
  if (usedDictionary) return;
  let audios = phrases.reduce((acc, curr) => [...acc, curr.audioLink], []);

  audios = JSON.stringify(audios);

  const cacheSentenceKey = getCacheSentenceKey(text, {
    voiceCode,
    audioType,
    speed,
    bitrate,
    sampleRate,
    awsZoneSynthesis,
  });

  await setKeyWithTtl(cacheSentenceKey, audios, REDIS_KEY_TTL.CACHE_AUDIO);
};

module.exports = { handleCacheSentence, getSynthesisSentences };
