const backgroundMusicService = require('../services/backgroundMusic');
const backgroundMusicDao = require('../daos/backgroundMusic');

const createBackgroundMusic = async (req, res) => {
  const { userId } = req.user;
  const { name, link } = req.body;

  const backgroundMusic = await backgroundMusicService.createBackgroundMusic(
    userId,
    name,
    link,
  );

  return res.send({ status: 1, result: backgroundMusic });
};

const getBackgroundMusics = async (req, res) => {
  const { search, fields, offset, limit, sort } = req.query;
  const { userId } = req.user;

  const query = {};
  query.query = { userId };
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  Object.keys(req.query)
    .filter(
      (q) => ['search', 'fields', 'offset', 'limit', 'sort'].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { backgroundMusics, total } =
    await backgroundMusicDao.findBackgroundMusics(query);

  return res.send({ backgroundMusics, metadata: { total } });
};

module.exports = { createBackgroundMusic, getBackgroundMusics };
