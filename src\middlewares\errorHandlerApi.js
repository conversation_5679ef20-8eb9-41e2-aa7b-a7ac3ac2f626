const snakecaseKeys = require('snakecase-keys');
const { API_V3_URL, RESPONSE_TYPE } = require('../constants');
const codes = require('../errors/code');
const getErrorMessage = require('../errors/message');
const { sendApiResponse } = require('../services/apiResponse');

// eslint-disable-next-line no-unused-vars
const errorHandler = (err, req, res, next) => {
  let statusCode = err.errorCode || err.statusCode;
  let { message: errorMessage } = err;
  const { url } = req;
  const { responseType } = req.body;

  const errorCode =
    err.errorCode || err.statusCode || codes.INTERNAL_SERVER_ERROR;
  switch (errorCode) {
    case codes.BAD_REQUEST:
      errorMessage = errorMessage || 'Bad Request';
      break;
    case codes.UNAUTHORIZED:
      errorMessage = 'Unauthorized';
      break;
    case codes.FORBIDDEN:
      errorMessage = 'Forbidden';
      break;
    case codes.NOT_FOUND:
      errorMessage = 'Not Found';
      break;
    case codes.TOO_MANY_REQUESTS:
      errorMessage = 'Too many requests';
      break;
    case codes.INTERNAL_SERVER_ERROR:
      statusCode = codes.INTERNAL_SERVER_ERROR;
      errorMessage = errorMessage || 'Something went wrong';
      break;

    default:
      errorMessage = errorMessage || getErrorMessage(errorCode);
      statusCode = 200;
  }
  const originalUrl = req.baseUrl ? `${req.baseUrl}${req.url}` : req.url;

  err.message = `${req.method.toUpperCase()} - ${originalUrl} - ${errorMessage}`;
  logger.error(err);

  if (responseType === RESPONSE_TYPE.DIRECT) {
    const isV3Version = url === API_V3_URL;
    const request = { isV3Version, ...req.body };
    sendApiResponse({ request, res, errorCode });
  } else
    res
      .status(statusCode)
      .send(
        snakecaseKeys({ errorCode, errorMsg: errorMessage }, { deep: true }),
      );
};

module.exports = errorHandler;
