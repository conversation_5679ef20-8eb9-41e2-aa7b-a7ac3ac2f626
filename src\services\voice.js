const moment = require('moment');
const spreadSheet = require('../utils/spreadSheet');
const voiceDao = require('../daos/voice');
const { getSortQuery } = require('../daos/utils/util');
const { findLanguages } = require('../daos/language');
const voiceCloningDao = require('../daos/voiceCloning');
const languageDao = require('../daos/language');
const {
  API_TTS_VERSION_DEFAULT,
  STUDIO_TTS_VERSION_DEFAULT,
} = require('../configs');
const {
  SYNTHESIS_TYPE,
  REQUEST_TYPE,
  REGEX,
  TTS_CORE_VERSION,
  VOICE_PROVIDER,
  PACKAGE_FEATURE,
} = require('../constants');
const CustomError = require('../errors/CustomError');
const errorCode = require('../errors/code');
const { VOICE_OWNERSHIP, LANGUAGE_CODES } = require('../constants/voice');
const { getClonedVoiceByCode } = require('./voiceCloning');
const { VC_VOICE_CODE_REGEX } = require('../constants/voiceCloning');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { hasSameElementIn2Array } = require('../utils/array');
const { getUserById } = require('./user');
const { PACKAGE_HAS_EOL } = require('../constants/package');

const createVoicesFromSpreadSheet = async ({
  sheetId,
  sheetName,
  sheetRange,
}) => {
  const getCode = (code) => {
    code = code.trim();
    return code;
  };

  const getName = (name) => {
    name = name.trim();
    return name;
  };

  const getProvider = (provider) => {
    provider = provider.toLowerCase().trim();
    switch (provider) {
      case 'vbee':
        return 'vbee';
      case 'google':
        return 'google';
      case 'amazon polly':
        return 'amazon';
      default:
        throw new Error(`Invalid provider: ${provider}`);
    }
  };

  const getLanguageCode = (language) => {
    language = language.trim();
    return language;
  };

  const getLanguageVietnameseName = (vietnameseName) => {
    vietnameseName = vietnameseName.trim();
    return vietnameseName;
  };

  const getLanguageGlobalName = (globalName) => {
    globalName = globalName.trim();
    return globalName;
  };

  const getGender = (gender) => {
    gender = gender.toLowerCase().trim();
    const validGenders = ['male', 'female'];
    if (validGenders.includes(gender)) return gender;
    throw new Error(`Invalid gender: ${gender}`);
  };

  const getType = (type) => {
    type = type.trim();
    const validTypes = ['Neural TTS', 'Standard TTS'];
    if (validTypes.includes(type)) return type;
    throw new Error(`Invalid type: ${type}`);
  };

  const data = await spreadSheet.getValues({ sheetId, sheetName, sheetRange });
  const voices = data.slice(1).map((row) => {
    const code = getCode(row[5]);
    const name = getName(row[6]);
    const gender = getGender(row[7]);
    const language = {
      code: getLanguageCode(row[1]),
      vietnameseName: getLanguageVietnameseName(row[2]),
      globalName: getLanguageGlobalName(row[3]),
    };
    const provider = getProvider(row[0]);
    const type = getType(row[4]);
    return { code, name, gender, language, provider, type };
  });

  await voiceDao.createVoices(voices);
};

const getVoiceByCode = async (code) => {
  if (!VOICES) {
    const { voices } = await voiceDao.findVoices();
    global.VOICES = voices;
  }

  const voiceExist = VOICES.find((voice) => voice.code === code);
  return voiceExist;
};

const createVoices = async (voices) => {
  const newVoices = await voiceDao.createVoices(voices);

  const { voices: updatedVoices } = await voiceDao.findVoices();
  global.VOICES = updatedVoices;

  return newVoices;
};

const updateVoice = async (voiceId, updateFields) => {
  const voice = await voiceDao.updateVoice(voiceId, updateFields);

  const { voices } = await voiceDao.findVoices();
  global.VOICES = voices;

  return voice;
};

const getVoiceByCodes = async (codes) => {
  if (!VOICES) {
    const { voices } = await voiceDao.findVoices();
    global.VOICES = voices;
  }
  const voices = VOICES.filter((voice) => codes.includes(voice.code));
  return voices;
};

const getVoiceFilterWithMultilingual = ({
  voices,
  languageCodeArray = [],
  genderArray = [],
  featuresArray = [],
  levelArray = [],
}) => {
  const isFilterMultilingual = languageCodeArray.includes(
    LANGUAGE_CODES.MULTILINGUAL,
  );
  languageCodeArray = languageCodeArray.filter(
    (lang) => lang !== LANGUAGE_CODES.MULTILINGUAL,
  );

  const filterVoices = voices.filter(
    (voice) =>
      (!languageCodeArray.length ||
        languageCodeArray.includes(voice.languageCode) ||
        hasSameElementIn2Array(
          languageCodeArray,
          voice?.secondaryLanguageCodes,
        )) &&
      (!genderArray?.length || genderArray.includes(voice.gender)) &&
      ((!levelArray?.length && !isFilterMultilingual) ||
        levelArray.includes(voice.level) ||
        (isFilterMultilingual &&
          voice.languageCode === LANGUAGE_CODES.MULTILINGUAL)) &&
      (!featuresArray?.length ||
        featuresArray.some((feature) => voice.features?.includes(feature))),
  );

  return filterVoices;
};

const getVoiceWithCreditFactor = (voice, packageCode) => {
  const creditFactor = getFeatureValue(FEATURE_KEYS.CREDIT_FACTOR, {
    voiceCode: voice?.code,
    voiceName: voice?.name,
    voiceLevel: voice?.level, // Provider vbee-voice-cloning has no level
    voiceProvider: voice?.provider,
    isBeta: voice?.beta,
    packageCode,
  });
  return {
    ...voice,
    creditFactor: creditFactor || 1, // Default to 1 if credit factor feature flag is not enabled
  };
};

const updateVoicesWithCreditFactors = async (voices, user) => {
  const { packageCode } = user || {};
  const voicesWithCreditFactors = voices.map((voice) => {
    return getVoiceWithCreditFactor(voice, packageCode);
  });
  return voicesWithCreditFactors;
};

const queryVoices = async ({
  userId,
  voices,
  query,
  useMultilingualVoices,
}) => {
  let user;
  if (userId) user = await getUserById(userId);
  voices = await updateVoicesByUserEolDate(voices, user);

  const {
    search,
    searchFields = ['name'],
    query: queryField,
    offset = 0,
    limit,
    fields = [],
    sort = ['rank_asc'],
    useMicrosoftVoices,
  } = query;
  let dataQuery = {};

  if (queryField) {
    const { gender, languageCode, features, level, ...otherQuery } = queryField;
    dataQuery = { ...otherQuery };

    const languageCodeArray = languageCode?.split(',') || [];
    const genderArray = gender?.split(',') || [];
    const featuresArray = features?.split(',') || [];
    const levelArray = level?.split(',') || [];

    if (useMultilingualVoices)
      voices = getVoiceFilterWithMultilingual({
        voices,
        languageCodeArray: languageCode ? languageCodeArray : [],
        genderArray: gender ? genderArray : [],
        featuresArray: features ? featuresArray : [],
        levelArray: level ? levelArray : [],
      });
    else
      voices = voices.filter(
        (voice) =>
          (!languageCode || languageCodeArray.includes(voice.languageCode)) &&
          (!gender || genderArray.includes(voice.gender)) &&
          (!level || levelArray.includes(voice.level)) &&
          (!features ||
            featuresArray.some((feature) => voice.features?.includes(feature))),
      );
  }

  if (!useMicrosoftVoices) {
    voices = voices.filter(
      (voice) => voice.provider !== VOICE_PROVIDER.MICROSOFT,
    );
  }

  const searchRegex = new RegExp(search, 'gi');

  // filter by query
  for (const key in dataQuery) {
    if (queryField[key]) {
      voices = voices.filter((voice) => {
        return voice[key] === queryField[key];
      });
    }
  }

  voices = voices.filter((voice) => {
    let check = false;
    for (const key of searchFields) {
      check = check || voice[key].match(searchRegex);
    }
    return check;
  });

  const total = voices.length;

  // Sort
  const sortQuery = getSortQuery(sort);
  voices = voices.sort((a, b) => {
    let compare;
    for (const [key, value] of Object.entries(sortQuery)) {
      if (value === 1) {
        compare = compare || a[key] - b[key];
      } else compare = compare || b[key] - a[key];
    }

    return compare;
  });

  // Pagination
  if (limit) voices = voices.slice(offset, offset + limit);
  else voices = voices.slice(offset);

  // Field selection
  if (fields && fields.length > 0) {
    voices = voices.map((voice) => {
      const newVoice = {};
      for (const key of fields) {
        newVoice[key] = voice[key];
      }
      return newVoice;
    });
  }

  // Enrich with language data
  if (!LANGUAGES) {
    const { languages } = await findLanguages();
    global.LANGUAGES = languages;
  }

  const detailVoices = voices.map((voice) => {
    const language = LANGUAGES.find((item) => item.code === voice.languageCode);
    return { ...voice, language };
  });

  const finalVoices = await updateVoicesWithCreditFactors(detailVoices, user);

  return { voices: finalVoices, total };
};

const findVoices = async (query = {}, ip) => {
  if (!VOICES?.length) {
    const { voices } = await voiceDao.findVoices();
    global.VOICES = voices;
  }
  const voices = VOICES;

  const useMultilingualVoices = await getFeatureValue(
    FEATURE_KEYS.MULTILINGUAL_VOICES,
    { ip },
  );

  const queryResult = await queryVoices({
    voices,
    query,
    useMultilingualVoices,
  });
  return queryResult;
};

const getVersionVoice = async ({
  requestType,
  synthesisType,
  voiceCode,
  ttsVersion,
  text,
  userId,
  userFeatures,
}) => {
  const defaultVersion =
    requestType === REQUEST_TYPE.API
      ? API_TTS_VERSION_DEFAULT
      : STUDIO_TTS_VERSION_DEFAULT;
  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE)
    return { ttsCoreVersion: defaultVersion };

  const voice = await getVoiceInfoByCode(voiceCode, userId, userFeatures);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  if (voice.active === false) throw new CustomError(errorCode.INACTIVE_VOICE);

  const useEmphasisV2 = getFeatureValue(FEATURE_KEYS.EMPHASIS_FEATURE_V2, {
    userId,
    voiceCode,
  });

  // if feature flag is enabled, we will not check the regex and use old version
  const checkEmphasisInText = !useEmphasisV2 && REGEX.ADVANCE_TAG.test(text);
  const suitableVersion = checkEmphasisInText
    ? TTS_CORE_VERSION.NEW
    : TTS_CORE_VERSION.OLD;

  const ttsCoreVersion =
    suitableVersion || ttsVersion || voice?.version || defaultVersion;
  return { voice, ttsCoreVersion };
};

const getEolDate = async (user) => {
  const packageCode = user?.studio?.oneTime?.packageCode;
  const eolDate = user?.studio?.oneTime?.eolDate;

  const hasValidPackage = packageCode && PACKAGE_HAS_EOL.includes(packageCode);
  const isValidDate = eolDate && moment(eolDate).isAfter(moment());

  return hasValidPackage && isValidDate ? eolDate : undefined;
};

const updateVoicesByUserEolDate = async (voices, user) => {
  if (!user) return voices;
  const shouldUpdateVoicesByUserEolDate = getFeatureValue(
    FEATURE_KEYS.UPDATE_VOICES_BY_USER_EOL_DATE,
    { userId: user?._id },
  );
  if (!shouldUpdateVoicesByUserEolDate) return voices;

  const eolDate = await getEolDate(user);
  if (eolDate) {
    return voices.map((voice) =>
      voice?.eolDate ? { ...voice, eolDate } : voice,
    );
  }

  return voices.filter((voice) => !voice?.eolDate);
};

const getVoicesV2 = async (query = {}, ip) => {
  const { user, voiceOwnership = VOICE_OWNERSHIP.VBEE } = query;

  if (!VOICES?.length) {
    const { voices: vbeeVoices } = await voiceDao.findVoices();
    global.VOICES = vbeeVoices;
  }
  let voices = VOICES;

  if (
    voiceOwnership === VOICE_OWNERSHIP.COMMUNITY ||
    voiceOwnership === VOICE_OWNERSHIP.PERSONAL
  ) {
    const { voices: voiceCloningVoices } =
      await voiceCloningDao.findVoiceCloningByOwnerShip({
        voiceOwnership,
        userId: user.userId,
      });

    voices = voiceCloningVoices.map((voice) => ({
      active: true,
      ...voice,
    }));
  }

  const useMultilingualVoices = getFeatureValue(
    FEATURE_KEYS.MULTILINGUAL_VOICES,
    { userId: user.userId, email: user.email, ip },
  );

  const queryResult = await queryVoices({
    userId: user.userId,
    voices,
    query,
    useMultilingualVoices,
  });
  return queryResult;
};

const getVoiceInfoByCode = async (voiceCode, userId, userFeatures = []) => {
  // ADVISE: use User role instead of using FF
  const hasVoiceCloning = userFeatures?.includes(
    PACKAGE_FEATURE.AI_VOICE_CLONING,
  );
  
  const usePublicVoiceCloning = getFeatureValue(
    FEATURE_KEYS.USE_PUBLIC_VOICE_CLONING,
    { userId, voiceCode },
  );

  const synthesisVoiceCloning =
    usePublicVoiceCloning ||
    getFeatureValue(FEATURE_KEYS.VOICE_CLONING, { userId }) ||
    hasVoiceCloning;

  if (!synthesisVoiceCloning) {
    const voice = await getVoiceByCode(voiceCode);
    return voice;
  }

  const isClonedVoice = VC_VOICE_CODE_REGEX.test(voiceCode);
  // ADVISE: use User role instead of using FF
  const voice = isClonedVoice
    ? await getClonedVoiceByCode({
        code: voiceCode,
        userId,
        canUsePublicVoiceCloning: usePublicVoiceCloning,
      })
    : await getVoiceByCode(voiceCode);

  return voice;
};

const getVoiceInfoByCodes = async (voiceCodes, userId, userFeatures) => {
  const voices = await getVoiceByCodes(voiceCodes);
  const hasVoiceCloning = userFeatures?.includes(
    PACKAGE_FEATURE.AI_VOICE_CLONING,
  );
  const usePublicVoiceCloning = getFeatureValue(
    FEATURE_KEYS.USE_PUBLIC_VOICE_CLONING,
    { userId },
  );
  const synthesisVoiceCloning =
    getFeatureValue(FEATURE_KEYS.VOICE_CLONING, { userId }) ||
    hasVoiceCloning ||
    usePublicVoiceCloning;

  if (!synthesisVoiceCloning) return voices;

  const clonedVoiceCodes = voiceCodes.filter((code) =>
    VC_VOICE_CODE_REGEX.test(code),
  );
  const clonedVoices = await Promise.all(
    clonedVoiceCodes.map(async (code) => {
      const voice = await getClonedVoiceByCode({
        code,
        userId,
        isValidateVoice: false,
        canUsePublicVoiceCloning: usePublicVoiceCloning,
      });
      return voice;
    }),
  );

  return [...voices, ...clonedVoices];
};

const addLanguageDetailToVoice = async (voice) => {
  let language = LANGUAGES.find((item) => item.code === voice.languageCode);
  if (language) return { ...voice, language };

  language = await languageDao.findLanguageByCode(voice.languageCode);
  return { ...voice, language };
};

module.exports = {
  createVoicesFromSpreadSheet,
  getVoiceByCode,
  createVoices,
  updateVoice,
  getVoiceByCodes,
  findVoices,
  getVersionVoice,
  getVoicesV2,
  getVoiceInfoByCode,
  addLanguageDetailToVoice,
  getVoiceInfoByCodes,
  getVoiceWithCreditFactor,
};
