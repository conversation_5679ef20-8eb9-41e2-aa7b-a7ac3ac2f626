FROM node:20.13-alpine AS builder
WORKDIR /app
COPY package.json .
RUN --mount=type=secret,id=npm_token \
  export NPM_TOKEN=$(cat /run/secrets/npm_token) && \
  echo "@vbee-holding:registry=https://npm.pkg.github.com" > .npmrc && \
  echo "//npm.pkg.github.com/:_authToken=$NPM_TOKEN" >> .npmrc && \
  npm i --production && \
  rm .npmrc

FROM node:20.13-alpine
WORKDIR /app
COPY --from=builder /app .
COPY src/ /app/src

CMD [ "node", "src/index.js" ]
