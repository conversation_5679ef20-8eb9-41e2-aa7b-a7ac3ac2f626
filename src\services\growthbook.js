const { setPolyfills, GrowthBook } = require('@growthbook/growthbook');
const crossFetch = require('cross-fetch');
const EventSource = require('eventsource');
const {
  GROWTH_BOOK_API_HOST,
  GROWTH_BOOK_CLIENT_KEY,
  LOADING_FEATURES_REALTIME_INTERVAL,
} = require('../configs');

setPolyfills({
  // ADVISE: we should unify the runtime to Node 18 or later and remove this polyfill (also remove the cross-fetch deps)  
  fetch: crossFetch, // Required for Node 17 or earlier
  EventSource, // Optional, can make feature rollouts faster
});

global.GROWTH_BOOK = new GrowthBook({
  apiHost: GROWTH_BOOK_API_HOST,
  clientKey: GROWTH_BOOK_CLIENT_KEY,
});

const loadFeaturesRealtime = async () => {
  try {
    await global.GROWTH_BOOK.loadFeatures({ timeout: 10000 });
  } catch (error) {
    logger.error(error, { ctx: 'LoadFeaturesRealtime' });
  } finally {
    // ADVISE: we should unify the runtime to Node 18 or later and remove this polyfill (also remove the cross-fetch deps)    
    setTimeout(loadFeaturesRealtime, LOADING_FEATURES_REALTIME_INTERVAL);
  }
};

const getFeatureValue = (featureKey, attributes) => {
  const growthBookLocal = new GrowthBook();
  const features = global.GROWTH_BOOK.getFeatures();
  growthBookLocal.setFeatures(features);
  growthBookLocal.setAttributes(attributes);

  const featureValue = growthBookLocal.getFeatureValue(featureKey);
  growthBookLocal.destroy();

  return featureValue;
};

module.exports = { loadFeaturesRealtime, getFeatureValue };
