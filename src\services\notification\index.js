const callApi = require('../../utils/callApi');
const { NOTIFICATION_URL } = require('../../configs');

const sendSlackNotification = async (messagePayload) => {
  try {
    const data = await callApi({
      method: 'POST',
      url: `${NOTIFICATION_URL}/api/v1/slack/send-message`,
      data: messagePayload,
    });
    return data.result;
  } catch (error) {
    return {};
  }
};

module.exports = { sendSlackNotification };
