// TODO: this can be treated as constants, and moved to `models` repo

module.exports = {
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  USER_BLOCK: 405,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,

  INVALID_VOICE_CODE: 1001,
  TEXT_TOO_LONG: 1002,
  USER_NOT_FOUND: 1003,
  PACKAGE_EXPIRED: 1004,
  REQUEST_NOT_FOUND: 1005,
  INVALID_SYNTAX: 1006,
  PACKAGE_NOT_EXIST: 1007,
  TTS_FAILURE: 1008,
  REQUEST_TIMEOUT: 1009,
  UNAVAILABLE_VOICE: 1010,
  EXCEED_DEMO_TTS: 1011,
  LIMITED_FEATURE: 1012,
  INVALID_SAMPLE_RATE: 1013,
  IS_HATE_SPEECH: 1014,
  B<PERSON><PERSON>K_WORD_EXIST: 1015,
  BLACK_WORD_NOT_EXIST: 1016,
  SENTENCES_NOT_SUPPORT_EMPHASIS: 1017,
  INVALID_AUDIO_TYPE: 1018,
  FILE_TOO_LARGE: 1019,
  TTS_CACHING_NOT_SUPPORT_THIS_VOICE: 1020,
  TEXT_DEMO_TOO_LONG: 1021,
  EXCEED_MAX_PREVIEW: 1022,
  EXCEED_PREVIEW_TTS: 1023,
  DEMO_FEATURE_UNSUPPORTED: 1024,
  VOICE_NOT_SUPPORT_DUBBING: 1025,
  DUBBING_AUDIO_TOO_LONG: 1026,
  INVALID_SRT_FORMAT: 1027,
  DUBBING_ONLY_SUPPORT_VIETNAMESE: 1028,
  DUBBING_SENTENCE_TOO_LONG: 1029,
  AUDIO_URL_EXPIRED: 1030,
  DOWNLOAD_QUOTA_EXCEEDED: 1031,
  RENAME_TITLE_FAILURE: 1032,
  STUDIO_BASIC_DEPRECATED: 1033,
  SPAM_EMAIL: 1034,
  EXCEED_CHARACTERS: 1035,
  INACTIVE_VOICE: 1036,
  NO_MORE_SUPPORT_VOICE: 1037,

  PROJECT_NOT_FOUND: 1040,
  INVALID_BLOCK: 1041,

  APP_NOT_FOUND: 2001,
  APP_NOT_ACTIVATED: 2002,

  AWS_ZONE_EXIST: 3001,

  DUBBING_DURATION_TOO_LONG: 4001,
  MISSING_DUBBING_PACKAGE: 4002,
  VBEE_DUBBING_ERROR: 4003,
  DUBBING_FAILURE: 4004,
  GET_PROJECT_FAILURE: 4005,
  UPDATE_PROJECT_STATUS_FAILURE: 4006,

  TIMEOUT: 'TIMEOUT',

  // Recaptcha v3
  MISSING_INPUT_SECRET: 'MISSING_INPUT_SECRET',
  INVALID_INPUT_SECRET: 'INVALID_INPUT_SECRET',
  MISSING_INPUT_RESPONSE: 'MISSING_INPUT_RESPONSE',
  INVALID_INPUT_RESPONSE: 'INVALID_INPUT_RESPONSE',
  RECAPTCHA_BAD_REQUEST: 'RECAPTCHA_BAD_REQUEST',
  TIMEOUT_OR_DUPLICATE: 'TIMEOUT_OR_DUPLICATE',
  BROWSER_ERROR: 'BROWSER_ERROR',
  MISSING_RECAPTCHA: 'MISSING_RECAPTCHA',
  BOT_DETECTED: 'BOT_DETECTED',
  // Recaptcha enterprise
  INVALID_REASON_UNSPECIFIED: 'INVALID_REASON_UNSPECIFIED',
  UNKNOWN_INVALID_REASON: 'UNKNOWN_INVALID_REASON',
  MALFORMED: 'MALFORMED',
  EXPIRED: 'EXPIRED',
  DUPE: 'DUPE',
  MISSING: 'MISSING',

  // Voice cloning
  VOICE_CLONING_VOICE_EXISTED: 5001,
  CALL_API_CREATE_VOICE_IN_TTS_GATE_FAILED: 5002,
  DISCARDED_VOICE_CLONING: 5003,

  // STT
  ERROR_UPLOAD: 6000,
  INVALID_FILE_TYPE: 6001,
  LIMIT_FILE_SIZE: 6002,
  LIMIT_FILE_COUNT: 6003,
  LIMIT_PART_COUNT: 6004,
  LIMIT_FIELD_KEY: 6005,
  LIMIT_FIELD_VALUE: 6006,
  LIMIT_FIELD_COUNT: 6007,
  LIMIT_UNEXPECTED_FILE: 6008,
  STT_INSUFFICIENT_SECONDS: 6009,
  STT_MAX_CCR_REACHED: 6010,
  STT_INVALID_SAMPLE_RATE: 6011,

  ACCOUNT_ERROR: 7000,
};
