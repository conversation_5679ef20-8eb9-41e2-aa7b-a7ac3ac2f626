/* eslint-disable import/no-dynamic-require */
module.exports = (app) => {
  require('fs')
    .readdirSync('src/routes')
    .forEach((fileName) => {
      if (fileName === 'index.js' || fileName === 'apiV3.js') return;
      if (['js'].indexOf(fileName.split('.').pop()) === -1) return;

      if (fileName === 'shortUrl.js') {
        app.use('/', require(`./${fileName}`));
        return;
      }

      app.use('/api/v1', require(`./${fileName}`));
    });
};
