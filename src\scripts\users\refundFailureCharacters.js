require('dotenv').config();
require('../../models');
require('../../services/kafka/producer');
require('../../services/redis');

const logger = require('../../utils/logger');
const Request = require('../../models/request');
const Tts = require('../../models/tts');
const {
  SYNC_CHARACTERS_EVENT,
  REQUEST_STATUS,
  REQUEST_TYPE,
  REDIS_KEY_PREFIX,
  PACKAGE_CODE,
  SME_PACKAGE_CODES,
} = require('../../constants');
const { redisClient } = require('../../services/redis');
const userService = require('../../services/user');

global.logger = logger;

const getUserRequests = async () => {
  const failureRequests = await Request.aggregate([
    { $match: { status: REQUEST_STATUS.FAILURE, refund: false } },
    {
      $group: {
        _id: { userId: '$userId', demo: '$demo', type: '$type' },
        numOfRequests: { $sum: 1 },
        requests: {
          $addToSet: {
            _id: '$_id',
            characters: '$characters',
            packageCode: '$packageCode',
          },
        },
      },
    },
  ]);

  const userRequests = failureRequests.reduce((acc, item) => {
    const { userId, demo, type } = item._id;
    const { numOfRequests, requests } = item;

    if (userId && !demo)
      return [...acc, { userId, numOfRequests, requests, type }];
    return acc;
  }, []);

  return userRequests;
};

const updateUserRedis = async (userRequests) => {
  for (const userRequest of userRequests) {
    const { userId, numOfRequests, type, requests } = userRequest;

    // Update redis for request
    const numPendAndInprReqKey =
      type === REQUEST_TYPE.STUDIO
        ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
        : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

    const numOfPendAndInprReqRedis = await redisClient.get(
      numPendAndInprReqKey,
    );

    const numOfPendAndInprReq = numOfPendAndInprReqRedis
      ? parseInt(numOfPendAndInprReqRedis, 10)
      : 0;

    const newNumOfPendAndInprReq =
      numOfPendAndInprReq > numOfRequests
        ? numOfPendAndInprReq - numOfRequests
        : 0;

    if (parseInt(numOfPendAndInprReq, 10) !== newNumOfPendAndInprReq) {
      await redisClient.set(numPendAndInprReqKey, newNumOfPendAndInprReq);
      logger.info(
        `Updated ${numPendAndInprReqKey} from ${numOfPendAndInprReq} to ${newNumOfPendAndInprReq}`,
        { ctx: 'RunScript' },
      );
    }

    // Update redis for tts
    const numPendAndInprTtsKey =
      type === REQUEST_TYPE.STUDIO
        ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_TTS}_${userId}`
        : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_TTS}_${userId}`;

    const numOfPendAndInprTtsRedis = await redisClient.get(
      numPendAndInprTtsKey,
    );

    const numOfPendAndInprTts = numOfPendAndInprTtsRedis
      ? parseInt(numOfPendAndInprTtsRedis, 10)
      : 0;

    if (numOfPendAndInprTts) {
      for (const request of requests) {
        const numOfTts = await Tts.countDocuments({ requestId: request._id });

        const newNumOfPendAndInprTts =
          numOfPendAndInprTts > numOfTts ? numOfPendAndInprTts - numOfTts : 0;

        await redisClient.set(numPendAndInprTtsKey, newNumOfPendAndInprTts);
        logger.info(
          `Updated ${numPendAndInprTtsKey} from ${numOfPendAndInprTts} to ${newNumOfPendAndInprTts}`,
          { ctx: 'RunScript' },
        );
      }
    }
  }
};

const refundCharacters = async (userRequests) => {
  for (const userRequest of userRequests) {
    const { userId, requests } = userRequest;

    for (const request of requests) {
      const packageCodes = [...SME_PACKAGE_CODES, PACKAGE_CODE.STUDIO_TRIAL];
      if (!packageCodes.includes(request.packageCode)) {
        await userService.updateByCharacters({
          event: SYNC_CHARACTERS_EVENT.REFUND,
          userId,
          requestId: request._id,
          characters: request.characters,
        });
      }
    }
    logger.info(`Refunded characters for user ${userId}`, { ctx: 'RunScript' });
  }
};

(async () => {
  logger.info('Start refund failure characters for user...', {
    ctx: 'RunScript',
  });

  const userRequests = await getUserRequests();
  logger.info(`Get user requests success: ${userRequests.length}`, {
    ctx: 'RunScript',
  });

  // Update redis
  await updateUserRedis(userRequests);
  logger.info(`Update redis success`, { ctx: 'RunScript' });

  // Refund characters
  await refundCharacters(userRequests);
  logger.info(`Refund characters success`, { ctx: 'RunScript' });

  logger.info('Refund failure characters for user successfully', {
    ctx: 'RunScript',
  });
})();
