const { KAFKA_TOPIC, SYNC_SECONDS_EVENT } = require('../../constants');
const { sendMessage } = require('../kafka/producer');

const spendSttSeconds = ({ userId, requestId, seconds }) => {
  try {
    sendMessage(KAFKA_TOPIC.STT_SECONDS_PROCESSING, {
      key: userId,
      value: {
        event: SYNC_SECONDS_EVENT.SPEND,
        userId,
        requestId,
        seconds,
      },
    });
  } catch (error) {
    // TODO: use centralized logger
    console.error(error, { ctx: 'SpendSttSeconds', userId, requestId, seconds });
  }
};

const refundSttSeconds = ({ userId, appId, requestId }) => {
  try {
    sendMessage(KAFKA_TOPIC.STT_SECONDS_PROCESSING, {
      key: userId,
      value: {
        event: SYNC_SECONDS_EVENT.REFUND,
        userId,
        appId,
        requestId,
      },
    });
  } catch (error) {
    // TODO: use centralized logger
    console.error(error, { ctx: 'RefundSttSeconds', userId, appId, requestId });
  }
};

module.exports = { spendSttSeconds, refundSttSeconds };
