const { REDIS_KEY_PREFIX } = require('../../constants');
const { incrCounterOnRedis, decrCounterOnRedis } = require('./util');

const incrTokenizerRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_REQUESTS);
};

const incrTokenizerInProgressRequests = () => {
  incrCounterOnRedis(
    REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_IN_PROGRESS_REQUESTS,
  );
};

const decrTokenizerInProgressRequests = () => {
  decrCounterOnRedis(
    REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_IN_PROGRESS_REQUESTS,
  );
};

const incrTokenizerFailedRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_FAILED_REQUESTS);
};

module.exports = {
  incrTokenizerRequests,
  incrTokenizerInProgressRequests,
  decrTokenizerInProgressRequests,
  incrTokenizerFailedRequests,
};
