const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const ttsController = require('../controllers/tts');
const { auth, hasRole } = require('../middlewares/auth');

/* eslint-disable prettier/prettier */
router.get('/admin/tts', auth, hasRole("view-requests"), asyncMiddleware(ttsController.getTts));
/* eslint-disable prettier/prettier */

module.exports = router;
