const {
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  TTS_PROCESSING_STEPS,
  START_STEP_NAME,
} = require('../constants');
const { redisClient, setKeyWithTtl, setKeyIfNotExists } = require('./redis');
const processingTimeDao = require('../daos/processingTime');

const getProcessingTimeKey = (requestId) =>
  `${REDIS_KEY_PREFIX.TTS_PROCESSING_TIME}_${requestId}`;

const getProcessingTime = async (requestId) => {
  const processingTimeKey = getProcessingTimeKey(requestId);
  const processingTime = await redisClient.get(processingTimeKey);
  return JSON.parse(processingTime) || {};
};

const setProcessingTime = async (requestId, processingTime) => {
  const processingTimeKey = getProcessingTimeKey(requestId);
  await setKeyWithTtl(
    processingTimeKey,
    JSON.stringify(processingTime),
    REDIS_KEY_TTL.TTS_PROCESSING_TIME,
  );
};

const deleteProcessingTime = async (requestId) => {
  const processingTimeKey = getProcessingTimeKey(requestId);
  await redisClient.del(processingTimeKey);
};

const calcAvgProcessingTime = (processingTime, characters) => {
  const avgTime = Math.round((processingTime / (characters || 1)) * 1000);
  return avgTime || 0;
};

const saveStepProcessingTime = async ({
  requestId,
  step,
  startTime = Date.now(),
  additionalFields,
}) => {
  const endTime = Date.now();
  const duration = endTime - startTime;

  const processingTime = await getProcessingTime(requestId);
  if (!processingTime && !additionalFields) return;

  const updatedProcessingTime = {
    ...processingTime,
    [`${step}Time`]: duration,
    ...additionalFields,
  };
  await setProcessingTime(requestId, updatedProcessingTime);
};

const checkSaveProcessingTime = async (requestId) => {
  const key = `${REDIS_KEY_PREFIX.SAVE_PROCESSING_TIME}_${requestId}`;

  const isFirstSave = await setKeyIfNotExists(key, 'true');
  const isSaved = !isFirstSave;
  return isSaved;
};

const saveProcessingTime = async ({
  requestId,
  startTime = Date.now(),
  status,
}) => {
  const isSaved = await checkSaveProcessingTime(requestId);
  if (isSaved) return;

  const endTime = Date.now();
  const totalProcessingTime = endTime - startTime;

  const processingTime = await getProcessingTime(requestId);
  const {
    userId,
    preProcessingTime,
    queueTime,
    sentenceTokenizerTime,
    synthesisTime,
    audioJoinerTime,
    characters,
  } = processingTime;

  processingTimeDao.createProcessingTime({
    _id: requestId,
    userId,
    characters,
    preProcessingTime,
    queueTime,
    sentenceTokenizerTime,
    synthesisTime,
    audioJoinerTime,
    avgTotalTime: calcAvgProcessingTime(totalProcessingTime, characters),
    totalTime: totalProcessingTime,
    status,
  });

  deleteProcessingTime(requestId);
};

const getCurrentStep = (processingTime = {}) => {
  const ttsProcessingSteps = Object.values(TTS_PROCESSING_STEPS);
  const stepTimeFieldNames = Object.values(TTS_PROCESSING_STEPS).map(
    (step) => `${step}Time`,
  );
  const originStepNames = [...stepTimeFieldNames];

  const lastStepProcessed =
    stepTimeFieldNames
      .reverse()
      .find((step) =>
        Object.prototype.hasOwnProperty.call(processingTime, step),
      ) || null;

  const stepProcessedIndex = originStepNames.indexOf(lastStepProcessed);
  const currentStep = ttsProcessingSteps[stepProcessedIndex + 1];

  return currentStep;
};

const getStartTimeFromStep = (processingTime, step) => {
  const startTime = processingTime[START_STEP_NAME[step]];
  return startTime;
};

module.exports = {
  saveStepProcessingTime,
  saveProcessingTime,
  getProcessingTime,
  getCurrentStep,
  getStartTimeFromStep,
};
