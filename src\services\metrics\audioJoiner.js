const { REDIS_KEY_PREFIX } = require('../../constants');
const { incrCounterOnRedis, decrCounterOnRedis } = require('./util');

const incrJoinerRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_REQUESTS);
};

const incrJoinerInProgressRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_IN_PROGRESS_REQUESTS);
};

const decrJoinerInProgressRequests = () => {
  decrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_IN_PROGRESS_REQUESTS);
};

const incrJoinerFailedRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_FAILED_REQUESTS);
};

module.exports = {
  incrJoinerRequests,
  incrJoinerInProgressRequests,
  decrJoinerInProgressRequests,
  incrJoinerFailedRequests,
};
