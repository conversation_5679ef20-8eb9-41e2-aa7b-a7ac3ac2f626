const { REDIS_KEY_PREFIX } = require('../../constants');
const { incrCounterOnRedis, decrCounterOnRedis } = require('./util');

const incrSynthesisRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_REQUESTS);
};

const incrSynthesisInProgressRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_IN_PROGRESS_REQUESTS);
};

const decrSynthesisInProgressRequests = () => {
  decrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_IN_PROGRESS_REQUESTS);
};

const incrSynthesisFailedRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_FAILED_REQUESTS);
};

module.exports = {
  incrSynthesisRequests,
  incrSynthesisInProgressRequests,
  decrSynthesisInProgressRequests,
  incrSynthesisFailedRequests,
};
