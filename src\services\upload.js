const callApi = require('../utils/callApi');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { UPLOAD_URL } = require('../configs');
const { readFile, deleteFile } = require('../utils/file');

const getPresignedUrlForUploading = async ({
  extension,
  fileName,
  accessToken,
}) => {
  try {
    const data = await callApi({
      method: 'GET',
      url: `${UPLOAD_URL}/api/v1/background-musics/presigned-url-for-uploading`,
      headers: { Authorization: `Bearer ${accessToken}` },
      params: { extension, fileName },
    });
    return data?.result?.url;
  } catch (error) {
    throw new CustomError(errorCodes.UPLOAD_ERROR, error.message);
  }
};

const getPresignedUrlForSharing = async (key) => {
  try {
    const data = await callApi({
      method: 'GET',
      url: `${UPLOAD_URL}/api/v1/background-musics/presigned-url-for-sharing`,
      params: { key },
    });
    return data?.result?.url;
  } catch (error) {
    throw new CustomError(errorCodes.UPLOAD_ERROR, error.message);
  }
};

const uploadFileByPresignedUrl = async (url, file) => {
  try {
    await callApi({
      method: 'PUT',
      url,
      data: file,
      headers: { 'Content-Type': 'application/octet-stream' }, // Binary file
      json: true, // Return json response
      maxContentLength: 100000000,
      maxBodyLength: 1000000000,
    });
  } catch (error) {
    throw new CustomError(errorCodes.UPLOAD_ERROR, error.message);
  }
};

const uploadToS3 = async (filePath, accessToken) => {
  const [fileName, extension] = filePath.split('.');

  const uploadUrl = await getPresignedUrlForUploading({
    extension,
    fileName,
    accessToken,
  });

  const file = await readFile(filePath);
  await uploadFileByPresignedUrl(uploadUrl, file);
  deleteFile(filePath);

  const fileUrl = uploadUrl.split('?')[0];
  const { pathname } = new URL(fileUrl);
  const key = pathname.substring(1);
  const sharingUrl = await getPresignedUrlForSharing(key);
  return sharingUrl;
};

module.exports = {
  getPresignedUrlForUploading,
  getPresignedUrlForSharing,
  uploadFileByPresignedUrl,
  uploadToS3,
};
