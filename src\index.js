require('dotenv').config();
require('./sentry');
// TODO: duplicated sentry? already call this inside ./sentry
const Sentry = require('@sentry/node');

// eslint-disable-next-line import/order
const { PORT, NEW_RELIC_ENABLED } = require('./configs');

if (NEW_RELIC_ENABLED) {
  require('newrelic');
}

const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const compression = require('compression');
const cors = require('cors');
const helmet = require('helmet');

const camelCaseReq = require('./middlewares/camelCaseReq');
const omitReq = require('./middlewares/omitReq');
const snakeCaseRes = require('./middlewares/snakeCaseRes');
const errorHandler = require('./middlewares/errorHandler');
const errorHandlerApi = require('./middlewares/errorHandlerApi');
const successReq = require('./middlewares/successReq');
const getDevice = require('./middlewares/getDevice');
const getIpInfo = require('./middlewares/getIpInfo');
const apiV3Route = require('./routes/apiV3');

const app = express();

app.use(cors());
app.use(helmet());
app.use(compression());
app.use(express.json({ limit: '10MB' }));
app.use(express.urlencoded({ extended: true }));
app.use(camelCaseReq);
app.use(omitReq);
app.use(snakeCaseRes());
app.use(successReq());
app.use(getDevice);
app.use(getIpInfo);
app.use(express.static(path.join(__dirname, '..', 'public')));

require('./routes')(app);
require('./routes/v2')(app);

app.use(errorHandler);

app.use('/', apiV3Route);
app.use(errorHandlerApi);

const server = http.createServer(app);

Sentry.setupExpressErrorHandler(app);

server.listen(PORT, () => {
  logger.info(`Server is running on port ${PORT}`);
});

global.WSS = new WebSocket.Server({ server, path: '/api/v1/synthesis' });

require('./models');
require('./services/init');
require('./services/redis');
