const sleep = require('../../utils/sleep');

const { getPublicKey, getAccessToken } = require('../iam');

const initPublicKey = async () => {
  let maxRetry = 5;
  while (!global.IAM_PUBLIC_KEY && maxRetry > 0) {
    try {
      const publicKey = await getPublicKey();
      global.IAM_PUBLIC_KEY = publicKey;
    } catch (error) {
      logger.error(error, { ctx: 'InitPublicKey' });
    } finally {
      maxRetry -= 1;
      await sleep(5 * 1000);
    }
  }

  if (!global.IAM_PUBLIC_KEY) {
    logger.error('Cannot get public key from IAM', { ctx: 'InitPublicKey' });
    process.exit(1);
  }
};

const startTokenRefresh = async () => {
  const token = await getAccessToken();
  global.IAM_ACCESS_TOKEN = token;

  const TOKEN_REFRESH_INTERVAL = 1 * 60 * 60 * 1000; // 1 hour
  setInterval(async () => {
    const accessToken = await getAccessToken();
    global.IAM_ACCESS_TOKEN = accessToken;
  }, TOKEN_REFRESH_INTERVAL);
};

initPublicKey();
startTokenRefresh();
