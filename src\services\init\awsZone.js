require('../../models');
const { PRIVATE_RSA_KEY_ENCRYPT_ACCESS_KEY } = require('../../configs');
const { REQUEST_TYPE } = require('../../constants');
const { FEATURE_KEYS } = require('../../constants/featureKeys');
const { getAwsZones } = require('../../daos/awsZone');
const { decryptAccessKey } = require('../../scripts/awsZone/encryptAccessKey');
const { getFeatureValue } = require('../growthbook');

const createArrayFollowWeight = (awsZones) => {
  const arrayFollowWeight = [];
  awsZones.forEach(({ region, weight }) => {
    for (let i = 0; i < weight; i += 1) {
      arrayFollowWeight.push(region);
    }
  });
  return arrayFollowWeight;
};

const initTtsCachingAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) =>
    allowRequestTypes.includes(REQUEST_TYPE.API_CACHING),
  );
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_CACHING = arrayFollowWeight;
};

const initTtsStudioAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) =>
    allowRequestTypes.includes(REQUEST_TYPE.STUDIO),
  );
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_STUDIO = arrayFollowWeight;
};

const initS3AccessMapping = async () => {
  const awsZones = (await getAwsZones()) || [];
  for (const awsZone of awsZones) {
    if (awsZone.s3Buckets && Object.values(awsZone.s3Buckets).length > 0) {
      // s3 bucket name is globally unique among all AWS accounts and regions
      const privateKey = PRIVATE_RSA_KEY_ENCRYPT_ACCESS_KEY;
      const s3AccessKeyId = decryptAccessKey({
        encryptedAccessKey: awsZone.s3AccessKeyId,
        privateKey,
      });
      const s3SecretAccessKey = decryptAccessKey({
        encryptedAccessKey: awsZone.s3SecretAccessKey,
        privateKey,
      });
      Object.values(awsZone.s3Buckets).forEach((bucketName) => {
        AWS_S3_ACCESS[bucketName] = {
          s3AccessKeyId,
          s3SecretAccessKey,
        };
      });
    }
  }
};

module.exports = { initTtsCachingAwsZone };

initTtsCachingAwsZone();
initTtsStudioAwsZone();

// Init s3 access mapping
// ADVISE: remote config, should be in centralized ConfigService (cachable, fallback, ...)
const decryptS3Key = getFeatureValue(FEATURE_KEYS.DECRYPT_S3_KEY);
if (decryptS3Key) initS3AccessMapping();
