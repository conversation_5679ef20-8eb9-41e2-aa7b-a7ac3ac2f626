const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const userController = require('../controllers/user');
const { auth, hasRole, setRole, authS2S } = require('../middlewares/auth');
const { S2S_ROLE } = require('../constants/roles');

/* eslint-disable prettier/prettier */
router.get('/users-not-reset-characters', auth, hasRole('get-users-not-reset-characters'), asyncMiddleware(userController.getUsersNotResetCharacter));
router.get('/users-not-reset-seconds', auth, hasRole('get-users-not-reset-characters'), asyncMiddleware(userController.getUsersNotResetSeconds));
router.get('/user-tts', auth, asyncMiddleware(userController.getUser));
router.get('/user-tts/:userId', setRole(S2S_ROLE.GET_USER_TTS), authS2S, asyncMiddleware(userController.getUserById));
router.put('/user-tts/:userId/lock-credits', setRole(S2S_ROLE.LOCK_CREDITS), authS2S, asyncMiddleware(userController.updateLockStudioCredits));
/* eslint-disable prettier/prettier */


module.exports = router;
