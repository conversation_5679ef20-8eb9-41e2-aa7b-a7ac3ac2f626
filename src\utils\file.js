const fs = require('fs');
const iconv = require('iconv-lite');
const chardet = require('chardet');
const https = require('https');
const path = require('path');
const CustomError = require('../errors/CustomError');

const readFile = (filePath) => {
  try {
    const result = fs.readFileSync(filePath);
    return result;
  } catch (e) {
    logger.error(`Get file failed at ${filePath}: `, { stack: e.stack });
    return null;
  }
};

const mkDirByPathSync = (targetDir, opts) => {
  const isRelativeToScript = opts && opts.isRelativeToScript;
  const { sep } = path;
  const initDir = path.isAbsolute(targetDir) ? sep : '';
  const baseDir = isRelativeToScript ? __dirname : '.';

  targetDir.split(sep).reduce((parentDir, childDir) => {
    const curDir = path.resolve(baseDir, parentDir, childDir);
    try {
      fs.mkdirSync(curDir);
    } catch (err) {
      if (err.code !== 'EEXIST') {
        throw err;
      }
    }

    return curDir;
  }, initDir);
};

const deleteFile = (filePath) => {
  fs.unlink(filePath, (err) => {
    if (err)
      logger.error(`Can't delete file ${filePath}`, {
        stack: err.stack,
        ctx: 'DeleteFile',
      });
  });
};

const readFileFromLink = async (link) => {
  try {
    const response = await new Promise((res) => https.get(link, res));
    if (response.statusCode !== 200) {
      throw new Error(
        `Error: ${response.statusCode} ${response.statusMessage}`,
      );
    }

    const chunks = [];

    response.on('data', (chunk) => {
      chunks.push(chunk);
    });

    return new Promise((resolve, reject) => {
      response.on('end', () => {
        const buffer = Buffer.concat(chunks);

        // Detect the encoding
        const detectedEncoding = chardet.detect(buffer);
        // Convert to UTF-8
        const utf8Content = iconv.decode(buffer, detectedEncoding);

        // The \r character appears when downloading files, need remove
        resolve(utf8Content.replace(/\r/g, ''));
      });

      response.on('error', (error) => {
        reject(error);
      });
    });
  } catch (error) {
    throw new CustomError(error, { ctx: 'ReadFileFromLink', link });
  }
};

module.exports = { readFile, mkDirByPathSync, deleteFile, readFileFromLink };
