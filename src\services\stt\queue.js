const { redisClient } = require('../redis');
const { REDIS_KEY_PREFIX } = require('../../constants');

const getNumOfPendAndInprReqKey = (userId) =>
  `${REDIS_KEY_PREFIX.API_STT_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

const getNumOfPendAndInprRequests = (userId) => {
  const numOfPendAndInprReqKey = getNumOfPendAndInprReqKey(userId);
  return redisClient.get(numOfPendAndInprReqKey);
};

const increaseNumOfPendAndInprRequests = async (userId) => {
  try {
    const numOfPendAndInprReqKey = getNumOfPendAndInprReqKey(userId);
    await redisClient.incr(numOfPendAndInprReqKey);
  } catch (error) {
    logger.error('Increase number of pending and inprogress requests failed', {
      ctx: 'ProcessRequestByCcr',
      userId,
      stack: error.stack,
    });
  }
};

const decreaseNumOfPendAndInprRequests = async (userId) => {
  try {
    const numOfPendAndInprReqKey = getNumOfPendAndInprReqKey(userId);
    await redisClient.decr(numOfPendAndInprReqKey);
  } catch (error) {
    logger.error('Increase number of pending and inprogress requests failed', {
      ctx: 'ProcessRequestByCcr',
      userId,
      stack: error.stack,
    });
  }
};

module.exports = {
  getNumOfPendAndInprRequests,
  increaseNumOfPendAndInprRequests,
  decreaseNumOfPendAndInprRequests,
};
