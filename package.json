{"name": "vbee-tts-api", "version": "1.0.0", "scripts": {"test": "node src/index.test.js", "start": "node src/index.js", "dev": "nodemon src/index.js"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-lambda": "^3.45.0", "@aws-sdk/client-s3": "^3.297.0", "@google-cloud/recaptcha-enterprise": "^5.1.0", "@google-cloud/storage": "^7.16.0", "@growthbook/growthbook": "^0.31.0", "@gurucore/lakdak": "^0.13.2", "@sentry/node": "^8.43.0", "@sentry/profiling-node": "^8.43.0", "@vbee-holding/node-logger": "^2.0.0", "@vbee-holding/vbee-node-shared-lib": "^1.0.3", "@vbee-holding/vbee-tts-models": "^0.0.6", "aes-js": "^3.1.2", "axios": "^0.21.4", "camelcase-keys": "^6.2.2", "chardet": "^2.0.0", "cheerio": "^1.0.0-rc.10", "compression": "^1.7.4", "cors": "^2.8.5", "cross-fetch": "^4.0.0", "csv-parser": "^3.0.0", "device-detector-js": "^3.0.3", "dotenv": "^8.2.0", "eventsource": "^2.0.2", "express": "^4.17.1", "express-mung": "^0.5.1", "express-validation": "^3.0.6", "franc": "^6.1.0", "gachchan": "^9.0.1", "google-auth-library": "^9.15.1", "googleapis": "^91.0.0", "helmet": "^3.23.3", "husky": "^7.0.2", "iconv-lite": "^0.6.3", "jsonwebtoken": "^8.5.1", "kafkajs": "^1.15.0", "md5": "^2.3.0", "moment": "^2.29.1", "mongoose": "7.7.0", "mongoose-long": "0.8.0", "multer": "^1.4.5-lts.1", "newrelic": "^9.15.0", "node-device-detector": "^2.1.0", "node-rsa": "^1.1.1", "prom-client": "^14.2.0", "redis": "^4.1.0", "retry": "^0.13.1", "snakecase-keys": "^3.2.0", "uuid": "^8.3.2", "wavefile": "^11.0.0", "ws": "^8.3.0", "yargs": "^17.7.2"}, "devDependencies": {"eslint": "^7.5.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^3.1.4", "lint-staged": "^11.2.0", "prettier": "^2.0.5"}, "lint-staged": {"*.js": "eslint"}}