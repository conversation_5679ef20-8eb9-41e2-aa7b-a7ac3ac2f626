const formatNumber = (value, defaultValue) => {
  // eslint-disable-next-line no-restricted-globals
  return value && !isNaN(value) ? parseInt(value, 10) : defaultValue;
};

const convertLongToNumber = (obj) => {
  if (obj instanceof Date) {
    return obj;
  }
  if (typeof obj !== 'object' || obj === null) return obj;

  if (
    'low' in obj &&
    'high' in obj &&
    typeof obj.low === 'number' &&
    typeof obj.high === 'number'
  ) {
    return obj.high * 2 ** 32 + obj.low; // Convert Long to Number
  }

  if (Array.isArray(obj)) {
    return obj.map(convertLongToNumber);
  }

  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      convertLongToNumber(value),
    ]),
  );
};

module.exports = { formatNumber, convertLongToNumber };
