const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const synthesisController = require('../controllers/synthesis');
const { auth } = require('../middlewares/auth');
const { blockBlackWords } = require('../middlewares/blockBlackWords');
const { checkBlock } = require('../middlewares/checkBlock');
const { checkRecaptcha } = require('../middlewares/recaptcha');
const { synthesisValidate } = require('../validations/synthesis');

/* eslint-disable prettier/prettier */
router.post('/synthesis', auth, synthesisValidate, checkBlock, blockBlackWords, checkRecaptcha, asyncMiddleware(synthesisController.synthesize));
/* eslint-disable prettier/prettier */


module.exports = router;
