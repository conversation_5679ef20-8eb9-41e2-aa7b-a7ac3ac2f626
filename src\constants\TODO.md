this folder is basically can be refactored to a shared `models` repository

to migrate safely, we import the `models` repository here, reassign the const with value from `models`

after a period of monitoring, we remove constants here (when everything is refer to `models` repository, not to this constants)

Should export an object, to be namespaced the constants, improve readability

```json
const DUBBING_AUDIO_DURATION_LIMIT = 4; // hours
const DUBBING_MAX_SENTENCE_LENGTH = 500; // characters
const DUBBING_SOURCE = {
  YOUTUBE: 'youtube',
  LOCAL: 'local',
  SRT: 'srt',
};
const SUBTITLE_FEATURE = 'subtitle';
module.exports = {
  DUBBING_AUDIO_DURATION_LIMIT,
  DUBBING_MAX_SENTENCE_LENGTH,
  DUBBING_SOURCE,
  SUBTITLE_FEATURE,
};
```

export `dubbing` object.
