const daoUtils = require('./utils');
const ClientPause = require('../models/clientPause');

const findClientPause = async (userId) => {
  const clientPause = await daoUtils.findOne(ClientPause, { userId });
  return clientPause;
};

const createClientPause = async (createFields) => {
  const clientPause = await ClientPause.create(createFields);
  return clientPause;
};

const updateClientPause = async (id, updateFields) => {
  const clientPause = await ClientPause.findByIdAndUpdate(id, updateFields, {
    new: true,
  });
  return clientPause;
};

module.exports = { findClientPause, createClientPause, updateClientPause };
