require('dotenv').config();
const fs = require('fs');
const moment = require('moment');
const uuid = require('uuid');

const logger = require('../../utils/logger');

global.logger = logger;
global.KAFKA_CONSUMER_GROUP_RANDOM_TTS = uuid.v4();

require('../../models');
require('../../services/kafka');

const Request = require('../../models/request');
const User = require('../../models/user');
const { REQUEST_STATUS, KAFKA_TOPIC } = require('../../constants');
const { sendMessage } = require('../../services/kafka/producer');

const unpaidCharactersFilePath = 'unpaidCharacters.csv';
const startTime = new Date('2022-07-10 19:30:00');
const endTime = new Date('2022-07-12 16:40:00');

const gatheringData = async () => {
  fs.writeFileSync(unpaidCharactersFilePath, `userId,characters\n`);

  const unpaidCharacters = await Request.aggregate([
    {
      $match: {
        createdAt: { $gte: startTime, $lte: endTime },
        packageCode: { $exists: true, $nin: ['STUDIO-TRIAL', 'STUDIO-BASIC'] },
        status: REQUEST_STATUS.SUCCESS,
        userId: { $exists: true },
        paid: false,
      },
    },
    { $group: { _id: '$userId', characters: { $sum: '$characters' } } },
  ]);

  unpaidCharacters.forEach((request) => {
    const { _id: userId, characters } = request;

    fs.appendFileSync(unpaidCharactersFilePath, `${userId},${characters}\n`);
  });

  logger.info(`Gathering data is done: ${unpaidCharacters.length}`, {
    ctx: 'RunScript',
  });
};

const convertCsvToJson = (filePath) =>
  new Promise((resolve, reject) => {
    const csv = require('csv-parser');
    const allData = [];

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => {
        allData.push(data);
      })
      .on('end', () => resolve(allData))
      .on('error', (err) => reject(err));
  });

const decreaseCharacters = async (user) => {
  try {
    const { userId } = user;
    const characters = Number(user.characters);

    // Get user data
    const userData = await User.findById(userId).lean();
    const {
      remainingCharacters: userRemainingCharacters,
      bonusCharacters: userBonusCharacters,
      lockCharacters: userLockCharacters,
      packageExpiryDate,
    } = userData;

    const isExpired = moment().isAfter(packageExpiryDate);

    const userUpdateFields = {};

    // Process user characters
    if (isExpired) {
      const lockCharacters = userLockCharacters - characters;
      userUpdateFields.lockCharacters = lockCharacters > 0 ? lockCharacters : 0;
    } else if (characters >= userBonusCharacters) {
      const bonusCharacters = 0;
      const remainingCharacters =
        userRemainingCharacters - (characters - userBonusCharacters);

      userUpdateFields.bonusCharacters = bonusCharacters;
      userUpdateFields.remainingCharacters =
        remainingCharacters > 0 ? remainingCharacters : 0;
    } else {
      const bonusCharacters = userBonusCharacters - characters;

      userUpdateFields.bonusCharacters =
        bonusCharacters > 0 ? bonusCharacters : 0;
    }

    // Update user characters
    const updatedUser = await User.findByIdAndUpdate(userId, userUpdateFields, {
      new: true,
    }).lean();

    // Sync user characters to account
    sendMessage(KAFKA_TOPIC.SYNC_CHARACTERS, {
      key: userId,
      value: {
        userId,
        remainingCharacters: updatedUser.remainingCharacters,
        bonusCharacters: updatedUser.bonusCharacters,
        lockCharacters: updatedUser.lockCharacters,
        syncCharactersAt: new Date(),
      },
    });

    await Request.updateMany(
      {
        createdAt: { $gte: startTime, $lte: endTime },
        packageCode: { $nin: ['STUDIO-TRIAL', 'STUDIO-BASIC'] },
        status: REQUEST_STATUS.SUCCESS,
        userId: { $exists: true },
        paid: false,
      },
      { paid: true },
    );
  } catch (error) {
    logger.error(error, { ctx: 'RunScript' });
  }
};

(async () => {
  await gatheringData();

  const unpaidCharacters = await convertCsvToJson(unpaidCharactersFilePath);

  for (const user of unpaidCharacters) {
    await decreaseCharacters(user);
  }

  fs.unlinkSync(unpaidCharactersFilePath);

  logger.info('Decrease characters is done', { ctx: 'RunScript' });
})();
