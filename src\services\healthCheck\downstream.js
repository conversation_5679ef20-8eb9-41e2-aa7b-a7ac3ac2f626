const {
  SERVICE,
  DOWNSTREAM_HEALTHCHECK_INTERVAL,
  IAM_URL,
  TTS_GATE_URL,
} = require('../../configs');
const { FEATURE_KEYS } = require('../../constants/featureKeys');
const { getFeatureValue } = require('../growthbook');
const callApi = require('../../utils/callApi');

let IS_DOWNSTREAM_HEALTHY = false;
const DOWNSTREAM_HEALTHCHECKS = [
  {
    downStreamUrl: IAM_URL,
    // eslint-disable-next-line no-return-await
    checkFn: async () => require('../iam').getAccessToken(),
    // eslint-disable-next-line no-unused-vars
    verifyFn: ({ error, result }) => {
      // logger.debug(result);
      if (error) return false;
      return !!result;
    },
  },
  {
    downStreamUrl: TTS_GATE_URL,
    checkFn: async () => checkHealthTtsGate(),
    // eslint-disable-next-line no-unused-vars
    verifyFn: ({ error, result }) => {
      // logger.debug(result);
      if (error) return false;
      return !!result;
    },
  },
];

const checkHealthTtsGate = async () => {
  const response = await callApi({
    method: 'GET',
    url: `${TTS_GATE_URL}/api/v1/uptime`,
  });
  return response;
};

const checkDownstream = async ({ downStreamUrl, checkFn, verifyFn }) => {
  let isHealthy = false;
  try {
    const result = await checkFn();
    isHealthy = verifyFn({ result });
  } catch (error) {
    logger.error(error, { ctx: 'DownstreamHealthcheck' });
    isHealthy = verifyFn({ error });
  } finally {
    logger.debug(`Healthcheck=${isHealthy}`, {
      ctx: 'DownstreamHealthcheck',
      downStreamUrl,
    });
  }
  return isHealthy;
};

const runCheckDownstreamHealthy = async () => {
  try {
    // ADVISE: remote config, should be in centralized ConfigService (cachable, fallback, ...)
    const checkHealth = getFeatureValue(FEATURE_KEYS.HEALTHCHECK, {
      service: SERVICE,
    });
    if (!checkHealth) return;

    // eslint-disable-next-line array-callback-return
    const results = await Promise.all(
      DOWNSTREAM_HEALTHCHECKS.map((check) => checkDownstream(check)),
    );
    IS_DOWNSTREAM_HEALTHY = results.every((result) => result);
  } catch (error) {
    logger.error(error, { ctx: 'DownstreamHealthcheck' });
  } finally {
    logger.debug(`Healthcheck=${IS_DOWNSTREAM_HEALTHY}`, {
      ctx: 'DownstreamHealthcheck',
    });
    setTimeout(runCheckDownstreamHealthy, DOWNSTREAM_HEALTHCHECK_INTERVAL);
  }
};

const isDownstreamReady = () => IS_DOWNSTREAM_HEALTHY;

module.exports = { isDownstreamReady, runCheckDownstreamHealthy };
