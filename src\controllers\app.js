const appService = require('../services/app');
const appDao = require('../daos/app');
const requestDao = require('../daos/request');

const createApp = async (req, res) => {
  const { userId } = req.user;
  const { name, expiresAt } = req.body;

  const app = await appService.createApp({
    name,
    userId,
    expiresAt,
  });

  return res.send({ app });
};

const getApps = async (req, res) => {
  const { search, searchFields, dateField, offset, limit, fields, sort } =
    req.query;
  const { userId } = req.user;
  const query = { 'members.userId': userId };

  Object.keys(req.query)
    .filter(
      (q) =>
        [
          'search',
          'searchFields',
          'dateField',
          'fields',
          'offset',
          'limit',
          'sort',
        ].indexOf(q) === -1,
    )
    .forEach((q) => {
      query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { total, apps } = await appService.getApps({
    search,
    searchFields,
    dateField,
    query,
    offset,
    limit,
    fields,
    sort,
  });

  return res.send({ apps, metadata: { total } });
};

const getAppsByAdmin = async (req, res) => {
  const {
    userId,
    search,
    searchFields,
    dateField,
    offset,
    limit,
    fields,
    sort,
  } = req.query;
  const query = {};
  if (userId) query['members.userId'] = userId;

  Object.keys(req.query)
    .filter(
      (q) =>
        [
          'search',
          'searchFields',
          'dateField',
          'fields',
          'offset',
          'limit',
          'sort',
        ].indexOf(q) === -1,
    )
    .forEach((q) => {
      query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { total, apps } = await appService.getApps({
    search,
    searchFields,
    dateField,
    query,
    offset,
    limit,
    fields,
    sort,
  });

  return res.send({ apps, metadata: { total } });
};

const updateApp = async (req, res) => {
  const { appId } = req.params;
  const { name, active } = req.body;

  const app = await appService.updateApp(appId, { name, active });

  return res.send({ app });
};

const getApp = async (req, res) => {
  const { appId } = req.params;

  const app = await appDao.findApp({ _id: appId });

  res.send({ app });
};

const getRequests = async (req, res) => {
  const { search, fields, offset, limit, sort } = req.query;
  const { appId } = req.params;
  const { userId } = req.user;
  const query = { 'members.userId': userId };

  query.query = { app: appId };
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  Object.keys(req.query)
    .filter(
      (q) => ['search', 'fields', 'offset', 'limit', 'sort'].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });
  const { requests, total } = await requestDao.findRequests(query);
  return res.send({ requests, metadata: { total } });
};

const syncApp = async (req, res) => {
  const { id: _id, ...appInfo } = req.body;

  const app = await appDao.updateApp(_id, { _id, ...appInfo });
  const { secretKey: appSecretKey, ...appWithoutSecretKey } = app;

  return res.send({ app: appWithoutSecretKey });
};

module.exports = {
  createApp,
  getApps,
  getAppsByAdmin,
  updateApp,
  getApp,
  getRequests,
  syncApp,
};
