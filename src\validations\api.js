const { Joi, validate } = require('express-validation');
const {
  AUDIO_TYPE,
  RESPONSE_TYPE,
  VALID_SPEED,
  VALID_BIT_RATE,
  VALID_BACKGROUND_MUSIC_VOLUME,
  OUTPUT_TYPE,
} = require('../constants');

const sentence = Joi.object().keys({
  inputText: Joi.string().required(),
  voiceCode: Joi.string().required(),
  speedRate: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
});

const synthesisApi = {
  body: Joi.object({
    appId: Joi.string().trim().required(),
    responseType: Joi.string()
      .valid(...Object.values(RESPONSE_TYPE))
      .optional(),
    outputType: Joi.string()
      .valid(...Object.values(OUTPUT_TYPE))
      .optional(),
    audioDomainType: Joi.string().optional(),
    callbackUrl: Joi.string()
      .uri()
      .trim()
      .when('responseType', {
        is: Joi.string().valid(RESPONSE_TYPE.INDIRECT),
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    inputText: Joi.string().trim().when('sentences', {
      not: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    voiceCode: Joi.string().when('inputText', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    sentences: Joi.array().min(1).items(sentence).optional(),
    audioType: Joi.string()
      .valid(...Object.values(AUDIO_TYPE))
      .optional(),
    bitrate: Joi.number()
      .valid(...VALID_BIT_RATE)
      .optional(),
    sampleRate: Joi.number().optional(),
    speedRate: Joi.number()
      .min(VALID_SPEED.MIN)
      .max(VALID_SPEED.MAX)
      .optional(),
    fromVn: Joi.boolean().optional(),
    backgroundMusic: Joi.object({
      link: Joi.string().uri().trim().required(),
      volume: Joi.number()
        .min(VALID_BACKGROUND_MUSIC_VOLUME.MIN)
        .max(VALID_BACKGROUND_MUSIC_VOLUME.MAX)
        .default(VALID_BACKGROUND_MUSIC_VOLUME.DEFAULT)
        .optional(),
    }).optional(),
    returnTimestampWords: Joi.boolean().optional(),
  }).without('inputText', 'sentences'),
};

module.exports = {
  synthesisApiValidate: validate(synthesisApi, { keyByField: true }),
};
