const moment = require('moment');
const User = require('../models/user');
const daoUtils = require('./utils');
const { PACKAGE_TYPE, PACKAGE_FEATURE, WALLET_TYPES } = require('../constants');

const updateUserById = async (userId, updateFields) => {
  const user = await User.findByIdAndUpdate(userId, updateFields, {
    new: true,
    upsert: true,
    omitUndefined: true,
  });
  return user;
};

const findUser = async (condition) => {
  const user = await daoUtils.findOne(User, condition);
  return user;
};

const findUserById = async (userId) => {
  const user = await User.findById(userId);
  return user;
};

const decreaseMaxPreview = async (userId) => {
  await User.findByIdAndUpdate(userId, { $inc: { maxPreview: -1 } });
};

const increaseUsedFreePreviews = async (userId) => {
  await User.findByIdAndUpdate(userId, { $inc: { usedFreePreviews: 1 } });
};

const findUsersNotResetCharacter = async ({
  packageCode,
  totalBonusCharacters,
  limit,
}) => {
  const startOfToday = moment().startOf('day').toDate();

  const pipeline = [
    { $match: { packageCode, bonusCharacters: { $lt: totalBonusCharacters } } },
    {
      $lookup: {
        from: 'orders',
        let: { userId: '$_id' },
        as: 'order',
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$userId', '$$userId'] },
              type: { $in: [PACKAGE_TYPE.STUDIO, null] },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
        ],
      },
    },
    { $unwind: '$order' },
    { $match: { 'order.lastResetBonusAt': { $lt: startOfToday } } },
    { $project: { _id: 1 } },
  ];
  if (limit) pipeline.push({ $limit: limit });

  const userIds = await User.aggregate(pipeline);

  return userIds.map((user) => user._id);
};

const findUsersNotResetSecond = async ({
  packageCode,
  totalBonusSeconds,
  limit,
}) => {
  const lastMonth = moment().subtract(30, 'days').startOf('day').toDate();

  const pipeline = [
    {
      $match: {
        'dubbing.packageCode': packageCode,
        'dubbing.remainingSeconds': { $lt: totalBonusSeconds },
      },
    },
    {
      $lookup: {
        from: 'orders',
        let: { userId: '$_id' },
        as: 'order',
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$userId', '$$userId'] },
              type: PACKAGE_TYPE.DUBBING,
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
        ],
      },
    },
    { $unwind: '$order' },
    { $match: { 'order.lastResetBonusDubbingAt': { $lte: lastMonth } } },
    { $project: { _id: 1 } },
  ];
  if (limit) pipeline.push({ $limit: limit });

  const userIds = await User.aggregate(pipeline);
  return userIds.map((user) => user._id);
};

const increaseTotalReqRecaptcha = async (userId) => {
  await User.findByIdAndUpdate(userId, { $inc: { 'recaptcha.totalReq': 1 } });
};

const increaseTotalInvalidReqRecaptcha = async (userId, isBrowserError) => {
  let increaseFields = { 'recaptcha.totalInvalidReq': 1 };
  if (isBrowserError) {
    increaseFields = {
      ...increaseFields,
      'recaptcha.totalBrowserErrorReq': 1,
      'recaptcha.numOfConsecutiveBrowserErrorReq': 1,
    };
  }
  await User.findByIdAndUpdate(userId, { $inc: increaseFields });
};

const resetConsecutiveBrowserErrorReqRecaptcha = async (userId) => {
  await User.findByIdAndUpdate(
    userId,
    { 'recaptcha.numOfConsecutiveBrowserErrorReq': 0 },
    { omitUndefined: true },
  );
};

const updateLatestDownloadedAt = async (userId, date = new Date()) => {
  await User.findByIdAndUpdate(userId, { latestDownloadedAt: date });
};

const updateDubbingLatestDownloadedAt = async (userId, date = new Date()) => {
  await User.findByIdAndUpdate(userId, { 'dubbing.latestDownloadedAt': date });
};

const updateStudioLatestDownloadedAt = async (userId, date = new Date()) => {
  await User.findByIdAndUpdate(userId, {
    latestDownloadedAt: date,
    'dubbing.latestDownloadedAt': date,
  });
};

const migrateVoiceCloningFeatures = async (userId) => {
  const updatedUser = await User.findOneAndUpdate(
    { _id: userId },
    { $addToSet: { features: PACKAGE_FEATURE.AI_VOICE_CLONING } },
    { new: true },
  ).lean();

  return updatedUser;
};

const removeRefPackageCodeByUserId = async (userId) => {
  await User.findByIdAndUpdate(userId, {
    $unset: { refPackageCode: '' },
  });
};

const updateLockStudioCycleCredits = async (userId, value = false) => {
  const user = await User.findByIdAndUpdate(userId, {
    'studio.cycle.isLocked': value,
  });

  return user;
};

const updateLockStudioTopUpCredits = async (userId, value = false) => {
  const user = await User.findByIdAndUpdate(userId, {
    'studio.topUp.isLocked': value,
  });

  return user;
};

const updateLockStudioCustomCredits = async (userId, value = false) => {
  const user = await User.findByIdAndUpdate(userId, {
    'studio.custom.isLocked': value,
  });

  return user;
};

const updateLockStudioCredits = async (
  userId,
  walletTypes = [],
  value = false,
) => {
  const updateFields = {};
  if (walletTypes.includes(WALLET_TYPES.STUDIO_CYCLE))
    updateFields['studio.cycle.isLocked'] = value;

  if (walletTypes.includes(WALLET_TYPES.STUDIO_TOP_UP))
    updateFields['studio.topUp.isLocked'] = value;

  if (walletTypes.includes(WALLET_TYPES.STUDIO_CUSTOM))
    updateFields['studio.custom.isLocked'] = value;

  if (Object.keys(updateFields).length === 0) return;

  await User.findByIdAndUpdate(userId, updateFields);
};

const unlockAllCredits = async (userId) => {
  await User.findByIdAndUpdate(userId, {
    'studio.cycle.isLocked': false,
    'studio.topUp.isLocked': false,
    'studio.custom.isLocked': false,
  });
};

module.exports = {
  updateUserById,
  findUser,
  findUserById,
  decreaseMaxPreview,
  increaseUsedFreePreviews,
  findUsersNotResetCharacter,
  findUsersNotResetSecond,
  increaseTotalReqRecaptcha,
  increaseTotalInvalidReqRecaptcha,
  resetConsecutiveBrowserErrorReqRecaptcha,
  updateLatestDownloadedAt,
  updateDubbingLatestDownloadedAt,
  updateStudioLatestDownloadedAt,
  migrateVoiceCloningFeatures,
  removeRefPackageCodeByUserId,
  updateLockStudioCycleCredits,
  updateLockStudioTopUpCredits,
  updateLockStudioCustomCredits,
  updateLockStudioCredits,
  unlockAllCredits,
};
