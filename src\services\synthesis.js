const uuid = require('uuid');
const moment = require('moment');
const {
  LOADING_SYNTHESIS_FACTOR,
  PACKAGE_FEATURE,
  REQUEST_TYPE,
  KAFKA_TOPIC,
  REQUEST_STATUS,
  SYNTHESIS_TYPE,
  REGEX,
  TTS_CORE_VERSION,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  VOICE_PROVIDER,
  AUDIO_DOMAIN_TYPE,
  SERVICE_TYPE,
  TTS_PROCESSING_STEPS,
  DEFAULT_BITRATE,
  BLOCK_SYNTHESIS_STATUS,
} = require('../constants');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const {
  getSynthesisType,
  countTextLength,
  validateText,
  getValidSampleRates,
  getMsClientPause,
  validateCredits,
  countCreditsByVoiceFactor,
  validateWalletCredits,
} = require('./preprocessing');
const { spendCharacters } = require('./characterProcessing');
const {
  handleTtsFailure,
  checkCompletedSynthesis,
  handleUpdateProgressTTS,
  handleTtsSuccess,
  updateProgressWhenSynthesisSuccessInRedis,
  handleStreamAudio,
  saveTtsIdsInRequest,
  callApiSynthesis,
  calProgressWhenSynthesisSuccess,
  findUsedWordsInPronunciationDict,
} = require('./ttsProcessing');
const { pushRequestToQueue, sendPendingRequestToKafka } = require('./queue');
const {
  saveSynthesisTime,
  createRequest,
  createRequestInRedis,
  modifyVNUrl,
  getTitle,
} = require('./request');
const {
  getVoiceByCode,
  getVersionVoice,
  getVoiceInfoByCode,
} = require('./voice');
const { sendMessage, sendMessages } = require('./kafka/producer');
const { redisClient } = require('./redis');

const { deleteInProgressRequest } = require('../daos/inProgressRequest');
const {
  findRequestById,
  // updateRequestById,
  updateRequestByIdInRedis,
  findRequestByIdInRedis,
  saveAudioLinkInRedis,
  updateFinalRequestFromRedisToDB,
  checkDoneTts,
} = require('../daos/request');
const {
  saveSentences,
  saveAudioInRedis,
  getAudiosInRedis,
  migrateTtsFromRedisToDB,
  updateFailureTTSInRedis,
  countRealTtsInRedis,
} = require('../daos/tts');
const { findUser, updateUserById } = require('../daos/user');
const { findDictionary } = require('../daos/dictionary');
const { findClientPause } = require('../daos/clientPause');
const { checkVoicePermission, canUseEOLVoice } = require('../utils/tts');
const { splitArray } = require('../utils/object');
const { handleCacheSentence, getSynthesisSentences } = require('./cache');
const { getAwsZone } = require('../daos/awsZone');
const {
  DEFAULT_NORMALIZER_FUNCTION,
  DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_T2A_FUNCTION,
  DEFAULT_SYNTHESIS_FUNCTION,
  DEFAULT_JOIN_AUDIO_FUNCTION,
  DEFAULT_BUCKET_S3,
  DEFAULT_AWS_REGION,
  MULTI_ZONE,
  TEXT_DEMO_MAX_LENGTH,
  SYNTHESIS_BY_GATEWAY,
  DEFAULT_AUDIO_TYPE,
} = require('../configs');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const {
  saveStepProcessingTime,
  saveProcessingTime,
  getProcessingTime,
} = require('./processingTime');
const { incrTotalInProgressRequests, incrTotalRequests } = require('./metrics');
const { processTtsDubbingToJoinAudios } = require('./dubbing');
const { copyCloudFile } = require('./audio');
const { sendEventCreateRequestToMoEngage } = require('./moengage');
const { getFeatureValue } = require('./growthbook');
const callApi = require('../utils/callApi');
// const { sendRequestToDataSenses } = require('./datasenses');
const {
  checkSpamAccount,
  isAdvancedGlobalVoice,
  shouldLockOneTimeCredits,
} = require('./user');
const { DEFAULT_CLIENT_PAUSE } = require('../constants/clientPause');
const { getSynthesisComputePlatform } = require('./synthesisComputePlatform');
const { getPackageUsageOptions } = require('./package');
const {
  updateBlockRequestId,
  updateBlockAudioLink,
  findProject,
} = require('../daos/project');

const getAwsZoneSynthesis = () => {
  const lengthAwsZones = AWS_ZONES_TTS_STUDIO.length;
  const randomIndex = Math.floor(Math.random() * lengthAwsZones);
  const awsZone = AWS_ZONES_TTS_STUDIO[randomIndex];
  return awsZone;
};

// ADVISE: the func is too long (really hard to understand/modify/test), split it into smaller funcs
const handleSynthesisRequest = async ({
  ip,
  device,
  deviceInfo,
  title = '',
  text,
  paragraphs,
  voiceCode,
  sentences = [],
  backgroundMusic,
  audioType = DEFAULT_AUDIO_TYPE,
  userId,
  email,
  createdAt = new Date(),
  speed = 1,
  bitrate,
  volume,
  demo = false,
  version: ttsVersion,
  serviceType,
  clientUserId,
  datasenses,
  isPronunciationPreview,
  projectId,
  blockId,
}) => {
  const blockedCredits = {};
  incrTotalInProgressRequests();
  incrTotalRequests();
  const startTime = Date.now();
  const requestId = uuid.v4();

  const awsZoneSynthesis = MULTI_ZONE
    ? getAwsZoneSynthesis() || DEFAULT_AWS_REGION
    : DEFAULT_AWS_REGION;

  const isUsingProject = Boolean(projectId);

  // From Landing page
  const isDemo = !userId && demo;
  const synthesisType = getSynthesisType({ text, sentences });
  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE)
    text = sentences.reduce((prev, curr) => prev + curr.text.trim(), '');

  const user = await findUser({ _id: userId });
  if (!user) throw new CustomError(code.USER_NOT_FOUND);
  const studioUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });

  // If feature flag is on, determine voice version can check and get voice info from voice cloning
  const { voice, ttsCoreVersion } = await getVersionVoice({
    requestType: REQUEST_TYPE.STUDIO,
    synthesisType,
    voiceCode,
    ttsVersion,
    text,
    userId,
    userFeatures: studioUsageOptions?.features,
  });

  let hasFreePreview = false; // This variable to develop the paid preview feature
  let textLength;

  const useEmphasisV2 = getFeatureValue(FEATURE_KEYS.EMPHASIS_FEATURE_V2, {
    userId,
    voiceCode,
  });

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW && !useEmphasisV2) {
    text = text.replace(
      REGEX.OLD_BREAK_TIME,
      (p1, p2) => `${`<break time="${p2}s"/>`}`,
    );
  }

  const isEmphasisVoice = voice?.styles && voice?.styles?.includes('emphasis');

  if (isEmphasisVoice && useEmphasisV2)
    text = text.replace(
      REGEX.NEW_BREAK_TIME,
      (p1, p2) => `${`<break time=${p2}s/>`}`,
    );

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW)
    textLength = countTextLength(text, REGEX.ADVANCE_TAG);
  else textLength = countTextLength(text);

  if (isDemo && textLength > TEXT_DEMO_MAX_LENGTH)
    throw new CustomError(code.TEXT_DEMO_TOO_LONG);

  // TODO: refactor

  if (!user.firstConvertAt && !isDemo) {
    updateUserById(userId, { firstConvertAt: new Date() }).catch((error) => {
      logger.error(error, { ctx: 'UpdateFirstConvertAt' });
    });
  }

  const {
    packageExpiryDate,
    remainingCharacters,
    bonusCharacters,
    packageCode,
  } = user;

  const {
    retentionPeriod,
    concurrentRequest,
    features,
    maxLengthInputText,
    maxLengthDemoInput,
  } = studioUsageOptions;

  if (!packageCode) throw new CustomError(code.PACKAGE_NOT_EXIST);
  if (demo && !features?.includes(PACKAGE_FEATURE.PREVIEW))
    throw new Error(code.LIMITED_FEATURE);

  if (moment().isAfter(packageExpiryDate, 'day'))
    throw new CustomError(code.PACKAGE_EXPIRED);

  const isSpamAccount = await checkSpamAccount({
    email,
    ip,
    packageCode,
    demo,
  });
  if (isSpamAccount) throw new CustomError(code.SPAM_EMAIL);

  if (demo) hasFreePreview = true;

  const useUserEolDate = getFeatureValue(
    FEATURE_KEYS.UPDATE_VOICES_BY_USER_EOL_DATE,
    { userId },
  );

  const configStorage = getFeatureValue(FEATURE_KEYS.CONFIG_STORAGE_TTS, {
    userId,
    ip,
  });

  const { bucket: gcsBucket = {} } = configStorage?.gcs || {};

  const sentencesCreditsInfo = await countCreditsByVoiceFactor({
    sentences: sentences.length ? sentences : [{ text, voiceCode }],
    ssmlRegex:
      ttsCoreVersion === TTS_CORE_VERSION.NEW ? REGEX.ADVANCE_TAG : undefined,
    user,
  });

  validateWalletCredits({
    demo,
    sentencesCreditsInfo,
    currWallets: {
      onetimeCredits: remainingCharacters + bonusCharacters,
      cycleCredits: user?.studio?.cycle?.remainingCredits || 0,
      topUpCredits: user?.studio?.topUp?.remainingCredits || 0,
      customCredits: user?.studio?.custom?.remainingCredits || 0,
    },
  });

  const credits = sentencesCreditsInfo.reduce(
    (acc, curr) => acc + curr.credits,
    0,
  );

  const awsZone = (await getAwsZone({ region: awsZoneSynthesis })) || {};
  const {
    normalizerFunction = DEFAULT_NORMALIZER_FUNCTION,
    sentenceTokenizerFunction = DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
    newSentenceTokenizerFunction = DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
    textToAllophoneFunction = DEFAULT_T2A_FUNCTION,
    synthesisFunction = DEFAULT_SYNTHESIS_FUNCTION,
    joinSentencesFunction = DEFAULT_JOIN_AUDIO_FUNCTION,
    defaultS3Bucket = DEFAULT_BUCKET_S3,
    s3Buckets = {},
  } = awsZone;
  const pronunciationDict = await findDictionary(userId);
  const clientPause = await findClientPause(userId);
  const { paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
    clientPause || {};
  const usedWordsInPronunciationDict = findUsedWordsInPronunciationDict(
    text,
    pronunciationDict?.words || [],
  );

  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE) {
    let isBlockOneTimeCredits = false;
    sentences = await Promise.all(
      sentences.map(async (sentence) => {
        const sentenceVoice = await getVoiceInfoByCode(
          sentence.voiceCode,
          userId,
          studioUsageOptions.features,
        );
        if (!sentenceVoice) throw new CustomError(code.INVALID_VOICE_CODE);

        const useLockOneTimeCredits = shouldLockOneTimeCredits({
          user,
          voice: sentenceVoice,
        });

        if (isAdvancedGlobalVoice(sentenceVoice) && useLockOneTimeCredits)
          isBlockOneTimeCredits = true;

        if (sentenceVoice?.active === false)
          throw new CustomError(code.INACTIVE_VOICE);

        if (sentenceVoice.version === TTS_CORE_VERSION.NEW)
          throw new CustomError(code.SENTENCES_NOT_SUPPORT_EMPHASIS);

        if (
          !isDemo &&
          !checkVoicePermission(features, sentenceVoice.features, user)
        )
          throw new CustomError(code.UNAVAILABLE_VOICE);

        const isEolVoice = sentenceVoice?.eolDate;
        if (
          isEolVoice &&
          useUserEolDate &&
          !canUseEOLVoice({ voice: sentenceVoice, user })
        )
          throw new CustomError(code.NO_MORE_SUPPORT_VOICE);

        let sentenceTextLength;
        const sentenceText = sentence.text;

        const isValidText = validateText({
          text: sentenceText,
          voiceProvider: sentenceVoice.provider,
          ttsCoreVersion,
        });
        if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

        if (ttsCoreVersion === TTS_CORE_VERSION.NEW) {
          sentenceTextLength = countTextLength(sentenceText, REGEX.ADVANCE_TAG);
        } else {
          sentenceTextLength = countTextLength(sentenceText);
        }

        return {
          ...sentence,
          text: sentenceText,
          characters: sentenceTextLength,
        };
      }),
    );

    if (isBlockOneTimeCredits) {
      blockedCredits.oneTimeCredits = true;

      validateCredits({
        demo,
        maxLengthDemoInput,
        maxLengthInputText,
        oneTimeCredits: remainingCharacters + bonusCharacters,
        cycleCredits: user?.studio?.cycle?.remainingCredits,
        topUpCredits: user?.studio?.topUp?.remainingCredits,
        customCredits: user?.studio?.custom?.remainingCredits,
        credits,
        textLength,
        blockedCredits,
      });
    }

    const firstVoice = await getVoiceInfoByCode(
      sentences[0].voiceCode,
      userId,
      studioUsageOptions?.features,
    );

    let request = {
      ip,
      device,
      deviceInfo,
      requestId,
      title: title || getTitle(text, firstVoice.languageCode),
      sentences,
      characters: textLength,
      credits,
      audioType,
      createdAt,
      status: REQUEST_STATUS.IN_PROGRESS,
      voiceCode: sentences[0].voiceCode,
      bitrate: bitrate || DEFAULT_BITRATE,
      volume,
      retentionPeriod,
      type: REQUEST_TYPE.STUDIO,
      pronunciationDict: usedWordsInPronunciationDict,
      clientPause: {
        paragraphBreak: paragraphBreak || DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
        sentenceBreak: sentenceBreak || DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
        majorBreak: majorBreak || DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
        mediumBreak: mediumBreak || DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
      },
      version: ttsCoreVersion,
      serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
      clientUserId,
      awsZoneSynthesis,
      awsZoneFunctions: {
        normalizerFunction,
        sentenceTokenizerFunction,
        newSentenceTokenizerFunction,
        textToAllophoneFunction,
        synthesisFunction,
        joinSentencesFunction,
        s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
      },
      googleCloudStorage: {
        bucket: gcsBucket[retentionPeriod],
      },
      datasenses,
    };
    if (packageCode) request.packageCode = packageCode;
    if (backgroundMusic) {
      request.backgroundMusic = backgroundMusic;
    }
    if (userId) request = { ...request, userId };

    const voiceCodes = sentences.reduce((acc, curr) => {
      if (curr.voiceCode && !acc.includes(curr.voiceCode))
        return [...acc, curr.voiceCode];
      return acc;
    }, []);
    const { maxSampleRate } = await getValidSampleRates(
      voiceCodes,
      userId,
      studioUsageOptions?.features,
    );
    if (!maxSampleRate)
      throw new CustomError(
        code.BAD_REQUEST,
        'Sentences with voices that are not combined',
      );
    request.sampleRate = maxSampleRate.toString();
    await createRequest(request);

    const cacheSentences = sentences.map((sentence) => {
      const { text: textSentence, ...cacheSentence } = sentence;
      return cacheSentence;
    });
    const cacheRequest = { ...request, sentences: cacheSentences };
    cacheRequest.voice = firstVoice;
    cacheRequest.progress = 0;
    const sentenceKeys = Array.from(Array(sentences.length).keys()).map(
      (index) => `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${index}`,
    );
    cacheRequest.sentenceKeys = sentenceKeys;
    cacheRequest.numberOfIndexSentences = sentences.length;
    cacheRequest.numberOfSentences = sentences.length;
    await createRequestInRedis(cacheRequest);
    saveStepProcessingTime({
      requestId,
      step: TTS_PROCESSING_STEPS.PRE_PROCESSING,
      startTime,
      additionalFields: {
        userId,
        characters: textLength,
        startTime,
        pushToQueueAt: Date.now(),
      },
    });

    const processRequestStatus = await pushRequestToQueue({
      userId,
      requestId,
      ccr: concurrentRequest,
      requestType: REQUEST_TYPE.STUDIO,
    });
    if (!processRequestStatus)
      throw new CustomError(
        code.INTERNAL_SERVER_ERROR,
        'Cannot push request to queue',
      );

    if (userId && !hasFreePreview) {
      spendCharacters({
        userId,
        requestId,
        characters: credits || textLength,
        blockedCredits,
        sentencesCreditsInfo,
      });
      sendMessage(KAFKA_TOPIC.TTS_CREATED, {
        value: {
          requestId,
          userId,
          characters: credits || textLength,
          blockedCredits,
          sentencesCreditsInfo,
        },
      });
    }

    const sentenceVoice = await getVoiceInfoByCode(
      sentences[0].voiceCode,
      userId,
      studioUsageOptions.features,
    );

    // Don't use await so it wouldn't affect the response time
    sendEventCreateRequestToMoEngage({ customerId: userId, request });

    request.voice = sentenceVoice;
    delete request.voiceCode;
    delete request.awsZoneFunctions;
    delete request.awsZoneSynthesis;
    delete request.datasenses;
    delete request.deviceInfo;
    return request;
  }
  if (!checkVoicePermission(features, voice.features, user))
    throw new CustomError(code.UNAVAILABLE_VOICE);

  const isEolVoice = voice?.eolDate;
  if (isEolVoice && useUserEolDate && !canUseEOLVoice({ voice, user }))
    throw new CustomError(code.NO_MORE_SUPPORT_VOICE);

  const isValidText = validateText({
    text,
    voiceProvider: voice.provider,
    ttsCoreVersion,
  });
  if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

  let request = {
    ip,
    device,
    deviceInfo,
    requestId,
    title: title || getTitle(text, voice.languageCode),
    text,
    paragraphs,
    characters: textLength,
    credits,
    audioType,
    speed,
    createdAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate: bitrate || DEFAULT_BITRATE,
    sampleRate: voice.defaultSampleRate.toString(),
    volume,
    demo,
    isPronunciationPreview,
    retentionPeriod,
    pronunciationDict: usedWordsInPronunciationDict,
    clientPause: {
      paragraphBreak: paragraphBreak || DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
      sentenceBreak: sentenceBreak || DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
      majorBreak: majorBreak || DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
      mediumBreak: mediumBreak || DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
    },
    type: REQUEST_TYPE.STUDIO,
    version: ttsCoreVersion,
    serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
    clientUserId,
    awsZoneSynthesis,
    awsZoneFunctions: {
      normalizerFunction,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      textToAllophoneFunction,
      synthesisFunction,
      joinSentencesFunction,
      s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
    },
    googleCloudStorage: {
      bucket: gcsBucket[retentionPeriod],
    },
    datasenses,
  };
  if (packageCode) request.packageCode = packageCode;
  if (backgroundMusic) {
    request.backgroundMusic = backgroundMusic;
  }
  if (userId) request = { ...request, userId };
  if (isUsingProject) {
    const project = await findProject({ userId, projectId });
    if (!project) throw new CustomError(code.BAD_REQUEST, 'Project not found!');
    request = { ...request, projectId, sampleRate: project.sampleRate };
  }

  request.synthesisComputePlatform = getSynthesisComputePlatform(request);

  // Prioritize request if it valid condition priority request and turn on synthesis by GPU
  const isPriorityRequest = getFeatureValue(
    FEATURE_KEYS.PROCESS_PRIORITIZE_REQUEST,
    {
      userId: request.userId,
      email,
      voiceCode: request.voiceCode,
      audioType: request.audioType,
      voice: voice.provider,
      characters: textLength,
      demo: request.demo,
    },
  );
  if (isPriorityRequest) request.isPriority = true;

  await createRequest(request);
  if (isUsingProject) {
    await updateBlockRequestId({
      projectId,
      blockId,
      requestId,
    });
  }

  const { text: textRequest, ...cacheRequest } = request;
  cacheRequest.numberOfSentences = 0;
  cacheRequest.voice = voice;
  cacheRequest.progress = 0;
  cacheRequest.sentenceKeys = [
    `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`,
  ];
  cacheRequest.numberOfIndexSentences = 1;
  await createRequestInRedis(cacheRequest);

  saveStepProcessingTime({
    requestId,
    startTime,
    step: TTS_PROCESSING_STEPS.PRE_PROCESSING,
    additionalFields: {
      userId,
      startTime,
      characters: textLength,
      pushToQueueAt: Date.now(),
    },
  });

  // TODO: limit preview tts by ip
  if (demo || hasFreePreview) {
    if (SYNTHESIS_BY_GATEWAY) callApiSynthesis(requestId);
    else sendPendingRequestToKafka({ requestId, userId });
  } else {
    const processRequestStatus = await pushRequestToQueue({
      userId,
      requestId,
      ccr: concurrentRequest,
      requestType: REQUEST_TYPE.STUDIO,
    });
    if (!processRequestStatus)
      throw new CustomError(
        code.INTERNAL_SERVER_ERROR,
        'Cannot push request to queue',
      );
  }

  if (userId && !hasFreePreview) {
    spendCharacters({
      userId,
      requestId,
      characters: credits || textLength,
      blockedCredits,
      sentencesCreditsInfo,
    });
    sendMessage(KAFKA_TOPIC.TTS_CREATED, {
      value: {
        requestId,
        userId,
        characters: credits || textLength,
        blockedCredits,
        sentencesCreditsInfo,
      },
    });
  }
  // Don't use await so it wouldn't affect the response time
  sendEventCreateRequestToMoEngage({ customerId: userId, request });

  delete request.voiceCode;
  request.voice = voice;
  request.progress = 0;
  request.blockId = blockId;
  delete request.awsZoneFunctions;
  delete request.awsZoneSynthesis;
  delete request.datasenses;
  delete request.deviceInfo;
  return request;
};

const downloadSentencesFile = async (url) => {
  const response = await callApi(url);
  return response;
};

const handleSentenceTokenizationSuccessResponse = async ({
  requestId,
  index,
  sentences,
  sentencesUrl,
  voiceCode,
  ttsCoreVersion,
  duration,
}) => {
  const request = await findRequestByIdInRedis(requestId);
  const processingTime = await getProcessingTime(requestId);
  const { startSentenceTokenizerAt } = processingTime;
  saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.SENTENCE_TOKENIZER,
    startTime: startSentenceTokenizerAt,
    additionalFields: { startSynthesisAt: Date.now() },
  });

  const {
    userId,
    ip,
    type: requestType,
    audioType,
    sampleRate,
    awsZoneSynthesis,
    awsZoneFunctions,
    type,
  } = request;

  const isDubbingRequest = requestType === REQUEST_TYPE.DUBBING;

  if (isDubbingRequest) sentences = await downloadSentencesFile(sentencesUrl);

  // TODO: refactor
  const { ttsIdRequests: savedSentences } = await saveSentences({
    requestId,
    index,
    sentences,
    voiceCode,
    type,
    audioType,
    sampleRate,
    awsZoneSynthesis,
  });

  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;
  const voice = await getVoiceByCode(voiceCode);

  await updateRequestByIdInRedis(requestId, {
    sentenceTokenizerDuration: duration,
    text: '', // remove text in cache
    synthesisDuration: {
      start: Date.now(),
    },
  });

  const progress = LOADING_SYNTHESIS_FACTOR.SENTENCE_TOKENIZATION * 100;
  await handleUpdateProgressTTS({ requestId, userId, progress });

  const dictionary = await findDictionary(request.userId);
  const clientPause = await findClientPause(request.userId);

  const user = await findUser({ _id: userId });
  const studioUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });

  const synthesisSentences = await getSynthesisSentences({
    sentences: savedSentences,
    request,
    index,
    dictionary,
    version: request.version,
    awsZoneSynthesis,
  });
  await saveTtsIdsInRequest(requestId);

  if (!synthesisSentences.length) return;
  const { textToAllophoneFunction, srtFunction } = awsZoneFunctions;
  const synthesisFunction =
    requestType === REQUEST_TYPE.DUBBING
      ? voice.synthesisFunction
      : awsZoneFunctions.synthesisFunction;

  const messages = synthesisSentences.reduce((acc, sentence) => {
    // id is ttsId
    const { text, subIndex, _id, start, end } = sentence;
    const breakTime = request.sentences?.length
      ? request.sentences[index].breakTime
      : 0;
    const value = {
      userId: userId || ip,
      requestId,
      requestType,
      ttsId: _id,
      index,
      subIndex,
      text:
        ttsCoreVersion !== TTS_CORE_VERSION.NEW &&
        request.sentences?.length &&
        voice.provider === VOICE_PROVIDER.VBEE &&
        breakTime
          ? `${text} <break time=${breakTime}s/>`
          : text,
      start,
      end,
      voice,
      audioType,
      speed: request.sentences?.length
        ? request.sentences[index].speed
        : request.speed,
      sampleRate: request.sampleRate,
      bitrate: request.bitrate,
      dictionary: dictionary?.words || [],
      clientPause: studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.CLIENT_PAUSE,
      )
        ? getMsClientPause(clientPause || {})
        : {},
      ttsCoreVersion,
      awsZoneSynthesis,
      textToAllophoneFunction,
      synthesisFunction,
      srtFunction,
      concurrentRequest: user?.concurrentRequest,
    };

    return [...acc, { value }];
  }, []);

  // Fix error: The request included a message larger than the max message size the server will accept
  const chunkSize = 100;
  const messageToSend = splitArray(messages, chunkSize);

  messageToSend.map((message) =>
    sendMessages(KAFKA_TOPIC.SYNTHESIS_REQUEST, message),
  );
};

const handleSentenceTokenizationFailureResponse = async ({
  requestId,
  errorCode,
  error,
}) => {
  const request = await findRequestByIdInRedis(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  const processingTime = await getProcessingTime(requestId);
  const { startSentenceTokenizerAt, startTime } = processingTime;
  await saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.SENTENCE_TOKENIZER,
    startTime: startSentenceTokenizerAt,
  });
  saveProcessingTime({ requestId, startTime, status: REQUEST_STATUS.FAILURE });

  const finalRequest = await updateRequestByIdInRedis(requestId, {
    status: REQUEST_STATUS.FAILURE,
    error,
    errorCode,
  });
  await deleteInProgressRequest(requestId);

  await updateFinalRequestFromRedisToDB(requestId, finalRequest);

  handleTtsFailure({
    request: finalRequest,
    errorCode,
    errorMessage: error,
  });
};

const handleSynthesisSuccessResponse = async ({
  requestId,
  index,
  text,
  ttsId,
  subIndex,
  audioName,
  audioLink,
  t2aDuration,
  synthesisDuration,
  phrases,
  cache,
}) => {
  await saveAudioInRedis({
    requestId,
    index,
    subIndex,
    ttsId,
    audioName,
    audioLink,
    t2aDuration,
    synthesisDuration,
    phrases,
  });

  // TODO: set redis and time to live
  const request = await findRequestByIdInRedis(requestId);
  // ADVISE: compose getRequestStatusFromRedis(), Find all occurences of hardcode "REQUEST_STATUS_", extract the business logic to a separate func.
  //#region getRequestStatusFromRedis
  
  // ADVISE: why there is the different of REDIS_KEY_PREFIX.REQUEST_STATUS and "REQUEST_STATUS_"?
  const statusRequest = await redisClient.get(`REQUEST_STATUS_${requestId}`);
  // value of updateStatusRequest > 0 means that request status was done
  if (
    statusRequest ||
    statusRequest > 0 ||
    request?.status === REQUEST_STATUS.FAILURE
  ) {
    const isDoneRequest = await checkDoneTts(requestId);
    if (isDoneRequest) await migrateTtsFromRedisToDB(requestId);
    return;
  }

  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  //#endregion

  if (request.demo) {
    handleStreamAudio({ requestId, index, subIndex, audioLink, phrases });
  }

  // Check data from cache or synthesis service
  if (
    !cache &&
    phrases?.length > 0 &&
    request.version === TTS_CORE_VERSION.NEW
  ) {
    const {
      userId,
      voice,
      audioType,
      speed,
      bitrate,
      sampleRate,
      awsZoneSynthesis,
    } = request;
    handleCacheSentence({
      userId,
      text,
      phrases,
      voiceCode: voice.code,
      audioType,
      speed,
      bitrate,
      sampleRate,
      awsZoneSynthesis,
    });
  }

  const completedTtsKey = `${REDIS_KEY_PREFIX.COMPLETED_SYNTHESIS}_${requestId}`;
  const numOfCompletedTts = await redisClient.incr(completedTtsKey);
  redisClient.expire(completedTtsKey, REDIS_KEY_TTL.COMPLETED_SYNTHESIS);

  const numOfTts = await countRealTtsInRedis(requestId);

  await updateProgressWhenSynthesisSuccessInRedis({
    requestId,
    userId: request.userId,
    progress: calProgressWhenSynthesisSuccess(numOfCompletedTts, numOfTts),
  });

  const isCompleted = await checkCompletedSynthesis({
    requestId,
    numberOfSentences: request.numberOfSentences || 1,
    numOfCompletedTts,
    numOfTts,
  });
  logger.info(
    {
      requestId,
      numberOfSentences: request.numberOfSentences || 1,
      numOfCompletedTts,
      isCompleted,
    },
    { ctx: 'HandleSynthesisSuccessResponse' },
  );

  if (isCompleted) {
    const requestStatusKey = `${REDIS_KEY_PREFIX.REQUEST_STATUS}_${requestId}`;
    await redisClient.incr(requestStatusKey);
    redisClient.expire(requestStatusKey, REDIS_KEY_TTL.REQUEST_STATUS);

    const {
      awsZoneSynthesis,
      awsZoneFunctions = {},
      sampleRate,
      bitrate,
      audioType,
      googleCloudStorage,
    } = request;
    const { joinSentencesFunction, s3Bucket, srtFunction } = awsZoneFunctions;
    const { bucket: gcsBucket } = googleCloudStorage || {};

    const processingTime = await getProcessingTime(requestId);
    const { startSynthesisAt } = processingTime;
    saveStepProcessingTime({
      requestId,
      step: TTS_PROCESSING_STEPS.SYNTHESIS,
      startTime: startSynthesisAt,
      additionalFields: { startJoinAudiosAt: Date.now() },
    });

    let message;
    if (request.type === REQUEST_TYPE.DUBBING) {
      const subtitles = await processTtsDubbingToJoinAudios(requestId);

      message = {
        value: {
          requestId,
          subtitles,
          sampleRate,
          bitrate,
          audioType,
          title: request.title,
          awsZoneSynthesis,
          srtFunction,
          s3Bucket,
          gcsBucket,
        },
      };
    } else {
      const audios = await getAudiosInRedis(requestId);
      message = {
        value: {
          requestId,
          audios,
          title: request.title,
          backgroundMusic: request.backgroundMusic,
          audioType: request.audioType,
          volume: request.volume,
          awsZoneSynthesis,
          joinSentencesFunction,
          s3Bucket,
          gcsBucket,
        },
      };
    }

    const checkJoinAudiosRequestKey = `${REDIS_KEY_PREFIX.JOIN_AUDIOS_REQUEST}_${requestId}`;
    const statusCheckJoinAudioRequest = await redisClient.get(
      checkJoinAudiosRequestKey,
    );

    logger.info(`StatusCheckJoinAudioRequest: ${statusCheckJoinAudioRequest}`, {
      ctx: 'StatusCheckJoinAudioRequest',
    });
    if (!statusCheckJoinAudioRequest) {
      await redisClient.incr(checkJoinAudiosRequestKey);
      redisClient.expire(
        checkJoinAudiosRequestKey,
        REDIS_KEY_TTL.JOIN_AUDIOS_REQUEST,
      );

      sendMessage(KAFKA_TOPIC.JOIN_AUDIOS_REQUEST, message);
      saveSynthesisTime(requestId, request?.synthesisDuration?.start);
    }
  }
};

const handleSynthesisFailureResponse = async ({
  requestId,
  ttsId,
  error,
  errorCode,
}) => {
  await updateFailureTTSInRedis({ requestId, ttsId, error });

  const statusRequest = await redisClient.get(
    `${REDIS_KEY_PREFIX.REQUEST_STATUS}_${requestId}`,
  );
  const request = await findRequestByIdInRedis(requestId);
  // value of updateStatusRequest > 0 means that request status was is done
  if (statusRequest) {
    const isDoneRequest = await checkDoneTts(requestId);
    if (isDoneRequest) await migrateTtsFromRedisToDB(requestId);
    return;
  }

  const requestStatusKey = `${REDIS_KEY_PREFIX.REQUEST_STATUS}_${requestId}`;
  await redisClient.incr(requestStatusKey);
  redisClient.expire(requestStatusKey, REDIS_KEY_TTL.REQUEST_STATUS);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  const finalRequest = await updateRequestByIdInRedis(requestId, {
    status: REQUEST_STATUS.FAILURE,
    error,
    errorCode,
  });

  const processingTime = await getProcessingTime(requestId);
  const { startSynthesisAt, startTime } = processingTime;
  await saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.SYNTHESIS,
    startTime: startSynthesisAt,
  });
  saveProcessingTime({ requestId, startTime, status: REQUEST_STATUS.FAILURE });

  deleteInProgressRequest(requestId);

  handleTtsFailure({
    request: finalRequest,
    errorCode: code.TTS_FAILURE,
    errorMessage: error,
  });

  await saveSynthesisTime(requestId, request?.synthesisDuration?.start);
  updateFinalRequestFromRedisToDB(requestId, finalRequest);
};

const handleJoinAudiosSuccessResponse = async ({
  requestId,
  audioLink: originalAudioLink,
  duration,
  ttsRequestId,
  timestampWords,
}) => {
  const request = await findRequestById(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  // Copy audio to another bucket for saving long time
  let audioLink = originalAudioLink;
  if (!request.audioLink)
    audioLink = await copyCloudFile(originalAudioLink, request);

  await updateRequestByIdInRedis(requestId, {
    progress: 100,
    joinAudioDuration: duration,
    ttsRequestId,
    timestampWords,
  });
  await deleteInProgressRequest(requestId);
  const { audioDomainType } = request;
  const finalAudioLink =
    audioDomainType === AUDIO_DOMAIN_TYPE.VN
      ? modifyVNUrl(audioLink)
      : audioLink;
  const finalRequest = await saveAudioLinkInRedis(requestId, finalAudioLink);
  await updateFinalRequestFromRedisToDB(requestId, finalRequest);

  const processingTime = await getProcessingTime(requestId);
  const { startJoinAudiosAt, startTime } = processingTime;
  await saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.AUDIO_JOINER,
    startTime: startJoinAudiosAt,
  });
  saveProcessingTime({ requestId, startTime, status: REQUEST_STATUS.SUCCESS });

  if (finalRequest?.projectId) {
    // No using await here since we only need to save it without using its result
    // and avoid blocking response time
    const audioExpiredAt = moment(finalRequest.endedAt).add(
      finalRequest.retentionPeriod,
      'days',
    );
    updateBlockAudioLink({
      projectId: finalRequest.projectId,
      requestId: finalRequest._id,
      audioLink: finalRequest.audioLink,
      status: BLOCK_SYNTHESIS_STATUS.SUCCESS,
      audioExpiredAt,
    }).catch((err) => {
      logger.error(err, {
        ctx: 'HandleJoinAudiosSuccessResponse',
        requestId,
        projectId: finalRequest.projectId,
        audioLink,
      });
    });
  }

  // sendRequestToDataSenses({ ...request, status: REQUEST_STATUS.SUCCESS });

  handleTtsSuccess(finalRequest);
};

const handleJoinAudiosFailureResponse = async ({
  requestId,
  ttsRequestId,
  error,
  errorCode,
}) => {
  const request = await findRequestByIdInRedis(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  const processingTime = await getProcessingTime(requestId);
  const { startJoinAudiosAt, startTime } = processingTime;
  await saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.AUDIO_JOINER,
    startTime: startJoinAudiosAt,
  });
  saveProcessingTime({ requestId, startTime, status: REQUEST_STATUS.FAILURE });

  const endedAt = new Date();
  const finalRequest = await updateRequestByIdInRedis(requestId, {
    status: REQUEST_STATUS.FAILURE,
    ttsRequestId,
    error,
    endedAt,
    errorCode,
  });

  updateFinalRequestFromRedisToDB(requestId, finalRequest);
  deleteInProgressRequest(requestId);
  migrateTtsFromRedisToDB(requestId);

  if (finalRequest?.projectId) {
    // No using await here since we only need to save it without using its result
    // and avoid blocking response time
    updateBlockAudioLink({
      projectId: finalRequest.projectId,
      requestId: finalRequest._id,
      audioLink: null,
      status: BLOCK_SYNTHESIS_STATUS.FAILURE,
    }).catch((err) => {
      logger.error(err, {
        ctx: 'HandleJoinAudiosFailureResponse',
        requestId,
        projectId: finalRequest.projectId,
      });
    });
  }

  // sendRequestToDataSenses(request);

  handleTtsFailure({
    request: finalRequest,
    errorCode: code.TTS_FAILURE,
    errorMessage: error,
  });
};

module.exports = {
  handleSynthesisRequest,
  handleSentenceTokenizationSuccessResponse,
  handleSentenceTokenizationFailureResponse,
  handleSynthesisSuccessResponse,
  handleSynthesisFailureResponse,
  handleJoinAudiosSuccessResponse,
  handleJoinAudiosFailureResponse,
};
