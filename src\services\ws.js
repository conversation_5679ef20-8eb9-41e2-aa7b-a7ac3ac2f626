const camelCaseKeys = require('camelcase-keys');
const snakeCaseKeys = require('snakecase-keys');
const uuid = require('uuid');
const moment = require('moment');

const code = require('../errors/code');
const getErrorMessage = require('../errors/message');
const userDao = require('../daos/user');

const {
  WS_MESSAGE_TYPE,
  CHECKING_ALIVE_CONNECTION_INTERVAL,
  TTS_CORE_VERSION,
  REGEX,
  REDIS_KEY_PREFIX,
  PACKAGE_CODE,
  FREE_PACKAGE_CODES,
  // REDIS_KEY_PREFIX,
} = require('../constants');
const {
  MAX_DEMO_TTS_LENGTH,
  MAX_PREVIEW_TTS_LENGTH,
  MAX_PREVIEW_TTS_TTL,
  MAX_PREVIEW_TTS,
  // MAX_DEMO_TTS,
  // MAX_DEMO_TTS_TTL,
} = require('../configs');

const { verifyAccessToken } = require('./auth');
const { handleSynthesisRequest } = require('./synthesis');
const { countTextLength } = require('./preprocessing');
const { redisClient } = require('./redis');

const { findUser } = require('../daos/user');
const { verifyRecaptchaDemoTTS } = require('./recaptcha');
const { getPackageUsageOptions } = require('./package');

global.REQUESTS = {};
global.CONNECTIONS = {};

const shutdown = () => {
  WSS.clients.forEach((ws) => {
    ws.close();
  });
  WSS.close();
};

const sendMessage = (ws, message) => {
  ws.isAlive = true;
  ws.send(JSON.stringify(snakeCaseKeys(message)));
};

const saveConnection = (ws) => {
  const { userId } = ws;
  CONNECTIONS[userId] = {
    ...CONNECTIONS[userId],
    [ws.connectionId]: ws,
  };
};

const sendMessageToAllSessions = (userId, message) => {
  if (!CONNECTIONS[userId]) return;

  const connections = Object.values(CONNECTIONS[userId]);
  connections.forEach((ws) => {
    sendMessage(ws, message);
  });
};

WSS.on('connection', (ws, req) => {
  const publicIP = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const connectionId = uuid.v4();
  ws.isAlive = true;
  ws.requestId = null;
  ws.connectionId = connectionId;

  let userId;

  ws.on('message', async (message) => {
    try {
      ws.isAlive = true;

      // Parse message to JSON
      try {
        message = message.toString();
        message = camelCaseKeys(JSON.parse(message), { deep: true });
      } catch (error) {
        logger.error('Parse message to JSON failed', {
          ctx: 'websocket',
          stack: error.stack,
        });
      }

      const { type, accessToken, payload, recaptchaToken } = message;
      switch (type) {
        case WS_MESSAGE_TYPE.PING: {
          sendMessage(ws, { type: WS_MESSAGE_TYPE.PONG });
          break;
        }

        case WS_MESSAGE_TYPE.INIT: {
          const payloadToken = await verifyAccessToken(accessToken);
          ({ sub: userId } = payloadToken);
          ws.userId = userId;
          saveConnection(ws);
          sendMessage(ws, { type: WS_MESSAGE_TYPE.INIT, status: 1 });
          break;
        }

        case WS_MESSAGE_TYPE.GET_REMAINING_PREVIEW: {
          const user = await findUser({ _id: userId });
          sendMessage(ws, {
            type: WS_MESSAGE_TYPE.GET_REMAINING_PREVIEW,
            status: 1,
            remainingPreview: user.maxPreview || 0,
            usedFreePreviews: user.usedFreePreviews || 0,
          });
          break;
        }

        case WS_MESSAGE_TYPE.SYNTHESIS: {
          // Verify recaptcha synthesis
          const userAgent = req.headers['user-agent'];
          const { platform } = req.headers;
          const appVersion = req.headers['app-version'];

          await verifyRecaptchaDemoTTS({
            recaptchaToken,
            platform,
            appVersion,
            userAgent,
            accessToken,
          });

          // TODO: Check if the request is valid by using joi
          const {
            title,
            text,
            voiceCode,
            audioType,
            backgroundMusic,
            speed,
            bitrate,
            volume,
            version,
            datasenses,
            isPronunciationPreview,
          } = payload;

          let textLength;
          if (version === TTS_CORE_VERSION.NEW)
            textLength = countTextLength(text, REGEX.ADVANCE_TAG);
          else textLength = countTextLength(text);

          const isPreview = !!ws.userId;

          // Prevent demo tts in landing page
          if (!isPreview) throw new Error(code.DEMO_FEATURE_UNSUPPORTED);

          // Limit demo tts in landing page
          // if (!isPreview) {
          //   const numOfDemoTtsKey = `${REDIS_KEY_PREFIX.DEMO_TTS}_${publicIP}`;
          //   const numOfDemoTts = await redisClient.incr(numOfDemoTtsKey);
          //   const expiryTime = moment().add(MAX_DEMO_TTS_TTL, 'hours');
          //   const endOfDay = moment().endOf('day');
          //   const demoTtsKeyExpiry = expiryTime.isBefore(endOfDay)
          //     ? expiryTime
          //     : endOfDay;

          //   const numOfDemoTtsKeyTtl = demoTtsKeyExpiry.diff(
          //     moment(),
          //     'seconds',
          //   );

          //   redisClient.expire(numOfDemoTtsKey, numOfDemoTtsKeyTtl);

          //   if (numOfDemoTts > MAX_DEMO_TTS)
          //     throw new Error(code.EXCEED_DEMO_TTS);
          // }

          const user = await findUser({ _id: userId });
          const studioUsageOptions = await getPackageUsageOptions({
            userId,
            packageCode: user.packageCode,
            userUsageOptions: user,
          });

          const maxLengthInputText =
            studioUsageOptions?.maxLengthDemoInput || MAX_PREVIEW_TTS_LENGTH;
          const overMaxLength = isPreview
            ? textLength > maxLengthInputText
            : textLength > MAX_DEMO_TTS_LENGTH;
          const maxPreview = studioUsageOptions?.maxPreview;
          if (overMaxLength) throw new Error(code.TEXT_TOO_LONG);

          const { isBlock } = user || {};
          if (isBlock) throw new Error(code.USER_BLOCK);

          const { packageCode, usedFreePreviews } = user || {};

          const isExceedMaxFreePreview =
            (packageCode &&
              FREE_PACKAGE_CODES.includes(packageCode) &&
              usedFreePreviews &&
              usedFreePreviews >= maxPreview) ||
            (packageCode === PACKAGE_CODE.STUDIO_TRIAL && maxPreview <= 0);

          if (isExceedMaxFreePreview) throw new Error(code.EXCEED_MAX_PREVIEW);

          if (packageCode !== PACKAGE_CODE.STUDIO_TRIAL && isPreview) {
            const numOfPreviewTtsKey = `${REDIS_KEY_PREFIX.PREVIEW_TTS}_${userId}`;
            const numOfPreviewTts = await redisClient.incr(numOfPreviewTtsKey);
            const expiryTime = moment().add(MAX_PREVIEW_TTS_TTL, 'hours');
            const endOfDay = moment().endOf('day');
            const previewTtsKeyExpiry = expiryTime.isBefore(endOfDay)
              ? expiryTime
              : endOfDay;

            const numOfPreviewTtsKeyTtl = previewTtsKeyExpiry.diff(
              moment(),
              'seconds',
            );

            redisClient.expire(numOfPreviewTtsKey, numOfPreviewTtsKeyTtl);
            if (numOfPreviewTts > MAX_PREVIEW_TTS)
              throw new Error(code.EXCEED_PREVIEW_TTS);
          }

          if (isPreview && !FREE_PACKAGE_CODES.includes(packageCode))
            userDao.decreaseMaxPreview(userId);

          if (isPreview && FREE_PACKAGE_CODES.includes(packageCode))
            userDao.increaseUsedFreePreviews(userId);

          const request = await handleSynthesisRequest({
            ip: publicIP,
            title,
            text,
            voiceCode,
            audioType,
            backgroundMusic,
            speed,
            bitrate,
            volume,
            demo: true,
            userId,
            version,
            datasenses,
            isPronunciationPreview,
          });

          ws.requestId = request.requestId;
          REQUESTS[request.requestId] = ws;

          const msg = {
            type: WS_MESSAGE_TYPE.SYNTHESIS,
            status: 1,
            result: request,
          };
          if (userId) sendMessageToAllSessions(userId, msg);
          else sendMessage(ws, msg);

          break;
        }

        default:
          break;
      }
    } catch (error) {
      logger.error(error, { ctx: 'websocket' });
      sendMessage(ws, {
        status: 0,
        error: error.message || getErrorMessage(error?.errorCode),
        code: error?.errorCode,
      });
    }
  });

  ws.on('close', () => {
    delete REQUESTS[ws.requestId];
  });
});

const terminateConnections = setInterval(() => {
  logger.info(`Checking ${[...WSS.clients].length} connections`, {
    ctx: 'websocket',
  });
  // eslint-disable-next-line consistent-return
  WSS.clients.forEach((ws) => {
    if (ws.isAlive === false) return ws.terminate();
    ws.isAlive = false;
  });
}, CHECKING_ALIVE_CONNECTION_INTERVAL);

WSS.on('close', function close() {
  clearInterval(terminateConnections);
});

module.exports = { sendMessage, sendMessageToAllSessions, shutdown };
