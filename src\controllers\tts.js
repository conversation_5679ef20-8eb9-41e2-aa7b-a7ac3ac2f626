const { SYNTHESIS_BY_GATEWAY } = require('../configs');
const ttsService = require('../services/tts');

const getTts = async (req, res) => {
  const { search, searchFields, offset, limit, sort } = req.query;

  const query = {};
  query.query = {};
  if (search) query.search = search;
  if (searchFields) query.searchFields = searchFields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');

  Object.keys(req.query)
    .filter(
      (q) => ['search', 'fields', 'offset', 'limit', 'sort'].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { tts, total } = await ttsService.getTts(
    SYNTHESIS_BY_GATEWAY ? req.query : query,
  );
  return res.send({ tts, total });
};

module.exports = { getTts };
