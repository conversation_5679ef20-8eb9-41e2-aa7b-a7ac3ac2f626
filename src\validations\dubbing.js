const { Joi, validate } = require('express-validation');
const {
  VALID_SPEED,
  AUDIO_TYPE,
  MAX_TITLE_CHARACTERS,
} = require('../constants');
const { DUBBING_SOURCE } = require('../constants/dubbing');

const dubbingApi = {
  body: Joi.object({
    title: Joi.string().trim().required(),
    subtitleLink: Joi.string().uri().trim().required(),
    voiceCode: Joi.string().trim().required(),
    speed: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    audioType: Joi.string()
      .valid(...Object.values(AUDIO_TYPE))
      .optional(),
    sampleRate: Joi.number().optional(),
    projectId: Joi.string().optional(),
    sentencesVoiceCode: Joi.object()
      .pattern(
        Joi.string().required(),
        Joi.array().items(Joi.number()).required(),
      )
      .optional(),
  }),
};

const dubbingWithVideoApi = {
  body: Joi.object({
    title: Joi.string().trim().required(),
    linkVideo: Joi.string().uri().trim().required(),
    voiceCode: Joi.string().trim().required(),
    speed: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    audioType: Joi.string()
      .valid(...Object.values(AUDIO_TYPE))
      .optional(),
    sampleRate: Joi.number().optional(),
    source: Joi.string()
      .valid(...Object.values(DUBBING_SOURCE))
      .required(),
    videoDuration: Joi.number().required(),
    originalLanguage: Joi.string().trim().optional(),
  }),
};

const createProject = {
  body: Joi.object({
    title: Joi.string().trim().min(1).max(MAX_TITLE_CHARACTERS).required(),
    voiceCode: Joi.string().trim().optional(),
    speed: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    subtitleLink: Joi.string().uri().trim().optional(),
    originalInfo: Joi.object({
      subtitleLink: Joi.string().optional(),
      language: Joi.string().required(),
      videoLink: Joi.string().optional(),
    })
      .optional()
      .xor('subtitleLink', 'videoLink'),
    targetLanguage: Joi.string().optional(),
  }),
};

const updateProjectById = {
  body: Joi.object({
    title: Joi.string().trim().min(1).max(50).optional(),
    voiceCode: Joi.string().trim().optional(),
    speed: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    currentSubtitleLink: Joi.string().uri().trim().optional(),
    status: Joi.string().optional(),
    latestRequestId: Joi.string().allow('').optional(),
    sentencesVoiceCode: Joi.object()
      .pattern(
        Joi.string().required(),
        Joi.array().items(Joi.number()).required(),
      )
      .optional(),
    originalInfo: Joi.object({
      subtitleLink: Joi.string().optional(),
      language: Joi.string().optional(),
      videoLink: Joi.string().optional(),
    }).optional(),
  }),
};

const deleteProjects = {
  body: Joi.object({
    projectIds: Joi.array().items(Joi.string().required()).min(1),
    isDeleteAll: Joi.boolean(),
  }).without('projectIds', 'isDeleteAll'),
};

module.exports = {
  dubbingApiValidation: validate(dubbingApi, { keyByField: true }),
  dubbingWithVideoApiValidation: validate(dubbingWithVideoApi, {
    keyByField: true,
  }),
  createProjectValidation: validate(createProject, { keyByField: true }),
  updateProjectByIdValidation: validate(updateProjectById, {
    keyByField: true,
  }),
  deleteProjectsValidation: validate(deleteProjects, { keyByField: true }),
};
