const prometheusClient = require('prom-client');
const { KAFKA_TOPIC, REDIS_KEY_PREFIX } = require('../../constants');
const {
  incrTokenizerInProgressRequests,
  decrTokenizerInProgressRequests,
  incrTokenizerFailedRequests,
  incrTokenizerRequests,
} = require('./sentenceTokenizer');
const {
  incrSynthesisInProgressRequests,
  decrSynthesisInProgressRequests,
  incrSynthesisFailedRequests,
  incrSynthesisRequests,
} = require('./synthesis');
const {
  incrJoinerInProgressRequests,
  decrJoinerInProgressRequests,
  incrJoinerFailedRequests,
  incrJoinerRequests,
} = require('./audioJoiner');
const { incrCounterOnRedis, decrCounterOnRedis } = require('./util');

const getMetrics = async () => {
  const metrics = await prometheusClient.register.metrics();
  return metrics;
};

const incrTotalRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_REQUESTS);
};

const incrTotalInProgressRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_PENDING_AND_INPROGRESS_REQUESTS);
};

const decrTotalInProgressRequests = () => {
  decrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_PENDING_AND_INPROGRESS_REQUESTS);
};

const incrTotalProcessingRequests = () => {
  incrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_INPROGRESS_REQUESTS);
};

const decrTotalProcessingRequests = () => {
  decrCounterOnRedis(REDIS_KEY_PREFIX.TOTAL_INPROGRESS_REQUESTS);
};

const calculateMetricBasedOnKafkaTopic = (kafkaTopic) => {
  switch (kafkaTopic) {
    case KAFKA_TOPIC.SENTENCE_TOKENIZATION_REQUEST: {
      incrTokenizerRequests();
      incrTokenizerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SENTENCE_TOKENIZATION_SUCCESS: {
      decrTokenizerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SENTENCE_TOKENIZATION_FAILURE: {
      decrTokenizerInProgressRequests();
      incrTokenizerFailedRequests();
      break;
    }

    case KAFKA_TOPIC.SYNTHESIS_REQUEST: {
      incrSynthesisRequests();
      incrSynthesisInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SYNTHESIS_SUCCESS: {
      decrSynthesisInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SYNTHESIS_FAILURE: {
      decrSynthesisInProgressRequests();
      incrSynthesisFailedRequests();
      break;
    }

    case KAFKA_TOPIC.JOIN_AUDIOS_REQUEST: {
      incrJoinerRequests();
      incrJoinerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.JOIN_AUDIOS_SUCCESS: {
      decrJoinerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.JOIN_AUDIOS_FAILURE: {
      decrJoinerInProgressRequests();
      incrJoinerFailedRequests();
      break;
    }

    default:
      break;
  }
};

module.exports = {
  getMetrics,
  incrTotalRequests,
  incrTotalInProgressRequests,
  decrTotalInProgressRequests,
  incrTotalProcessingRequests,
  decrTotalProcessingRequests,
  calculateMetricBasedOnKafkaTopic,
};
