const { getIp } = require('../services/ip');

const getIpInfo = (req, res, next) => {
  const { headers } = req;

  // TODO: should assign to req.__ExtraInfo, to avoid override other middleware
  Object.assign(req, {
    publicIP: getIp(headers),
    country: headers['cloudfront-viewer-country'] || '',
    countryName: headers['cloudfront-viewer-country-name'] || '',
    region: headers['cloudfront-viewer-country-region'] || '',
    regionName: headers['cloudfront-viewer-region-name'] || '',
    city: headers['cloudfront-viewer-city'] || '',
  });

  return next();
};

module.exports = getIpInfo;
