const Sentry = require('@sentry/node');
const { nodeProfilingIntegration } = require('@sentry/profiling-node');
const {
  SENTRY_DSN,
  SENTRY_TRACES_SAMPLE_RATE,
  SENTRY_PROFILES_SAMPLE_RATE,
  ENV,
  SENTRY_ENABLED,
} = require('./configs');

if (SENTRY_ENABLED)
  Sentry.init({
    dsn: SENTRY_DSN,
    integrations: [nodeProfilingIntegration()],
    environment: ENV,

    tracesSampleRate: SENTRY_TRACES_SAMPLE_RATE,
    profilesSampleRate: SENTRY_PROFILES_SAMPLE_RATE,
  });

  // TODO: should export the Sentry object and use it outside (the outside should not use const Sentry = require('@sentry/node'); anymore?)