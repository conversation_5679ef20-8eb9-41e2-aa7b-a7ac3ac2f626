const mongoose = require('mongoose');
const {
  VOICE_GENDER,
  VOICE_LOCALE,
  VOICE_STATUS,
  LANGUAGE_CODE,
} = require('../constants/voiceCloning');
const { VOICE_PROVIDER, VOICE_LEVEL } = require('../constants');

const voiceCloningSchema = new mongoose.Schema(
  {
    userId: String,
    code: String,
    name: String,
    image: String, // avatar
    gender: {
      type: String,
      enum: Object.values(VOICE_GENDER),
    },
    locale: {
      type: String,
      enum: Object.values(VOICE_LOCALE),
    },
    province: String,
    status: {
      type: String,
      enum: Object.values(VOICE_STATUS),
    },
    languageCode: {
      type: String,
      enum: Object.values(LANGUAGE_CODE),
      default: LANGUAGE_CODE.VI,
    },
    type: String,
    provider: {
      type: String,
      enum: Object.values(VOICE_PROVIDER),
    },
    squareImage: String, // avatar
    roundImage: String, // avatar
    demo: String,
    rank: Number,
    features: [String],
    styles: [String], // categories
    sampleRates: [Number],
    defaultSampleRate: Number,
    synthesisFunction: String,
    global: { type: Boolean, default: false },
    level: {
      type: String,
      enum: Object.values(VOICE_LEVEL),
    },
    version: String,
    beta: Boolean,
    isSample: { type: Boolean, default: false },
    sample: {
      style: String,
      audioLink: String,
      text: String,
    },
    hasDubbing: { type: Boolean, default: false },
    retentionDays: Number,
    discardAt: Date,
  },
  {
    versionKey: false,
  },
);

module.exports = mongoose.model('VoiceCloning', voiceCloningSchema);
