const CustomError = require('../errors/CustomError');
const sttService = require('../services/stt/apiStt');
const errorCodes = require('../errors/code');
const {
  validateRecognizeUploadFileRequest,
} = require('../services/stt/sttProcessing');

const apiStt = async (req, res) => {
  const { publicIP: ip, apiApp: app } = req;

  const { responseType, callbackUrl } = req.body;

  const { audioUrl, audioDuration, configAudio } =
    await validateRecognizeUploadFileRequest(req);

  const request = await sttService.handleSttRequest({
    ip,
    responseType,
    callbackUrl,
    fileUrl: audioUrl,
    app,
    audioDuration,
    configAudio,
  });

  return res.send(request);
};

const apiCallbackResponse = async (req, res) => {
  const {
    status,
    errorCode,
    errorMessage,
    result,
    clientRequestId: requestId,
  } = req.body;
  const { id: gateRequestId, text, textRaw } = result || {};

  const request = await sttService.handleSttCallback(requestId, {
    status,
    text,
    textRaw,
    errorCode,
    errorMessage,
    gateRequestId,
  });

  return res.send(request);
};

const getSttRequest = async (req, res) => {
  const { requestId } = req.params;
  const { authorization } = req.headers;

  if (!authorization) throw new CustomError(errorCodes.UNAUTHORIZED);
  const [tokenType, accessToken] = authorization.split(' ');
  if (tokenType !== 'Bearer') throw new Error(errorCodes.UNAUTHORIZED);

  const request = await sttService.getSttRequest({
    requestId,
    appToken: accessToken,
  });

  return res.send(request);
};

module.exports = { apiStt, apiCallbackResponse, getSttRequest };
