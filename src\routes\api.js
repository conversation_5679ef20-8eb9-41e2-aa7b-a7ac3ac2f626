const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const synthesisApiController = require('../controllers/api');
const cachingRequestController = require('../controllers/caching');
const requestController = require('../controllers/request');
const callbackResultController = require('../controllers/callbackResult');
const { authAPI } = require('../middlewares/auth');
const { checkBlockApi } = require('../middlewares/checkBlock');
const {
  cachingRequestValidate,
  countTextLengthValidate,
} = require('../validations/caching');
const { synthesisApiValidate } = require('../validations/api');
const { blockBlackWords } = require('../middlewares/blockBlackWords');

/* eslint-disable prettier/prettier */
router.post('/tts', authAPI, checkBlockApi, synthesisApiValidate, blockBlackWords, asyncMiddleware(synthesisApiController.apiSynthesis));
router.get('/tts/:requestId', asyncMiddleware(requestController.getApiRequest));
router.get('/tts/:requestId/callback-result', asyncMiddleware(callbackResultController.getCallbackResult));
router.post('/tts/caching', authAPI, checkBlockApi, cachingRequestValidate, asyncMiddleware(cachingRequestController.cachingSynthesis));
router.post('/tts/caching/text-length', authAPI, checkBlockApi, countTextLengthValidate, asyncMiddleware(cachingRequestController.countTextLength));

// callback from tts gate
router.put('/tts/update-request-progress', asyncMiddleware(synthesisApiController.updateRequestProgress));
router.post('/tts/callback-tts-gate', asyncMiddleware(synthesisApiController.apiCallbackResponse));

/* eslint-disable prettier/prettier */

module.exports = router;
