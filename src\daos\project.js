const { BLOCK_SYNTHESIS_STATUS } = require('../constants');
const Project = require('../models/project');
const daoUtils = require('./utils');

const createProject = async (projectInfo) => {
  const project = await Project.create(projectInfo);
  return project.toJSON();
};

const findProjects = async ({ userId, sort, limit, offset, search }) => {
  const { documents: projects, total } = await daoUtils.find(Project, {
    query: {
      userId,
      ...(search && { title: { $regex: search, $options: 'i' } }),
    },
    sort,
    limit,
    offset,
  });
  return { projects, total };
};

const findProject = async ({ userId, projectId }) => {
  const project = await daoUtils.findOne(Project, { _id: projectId, userId });
  return project;
};

const updateProject = async ({ projectId, title, blocks, audioType }) => {
  const project = await daoUtils.updateOne(
    Project,
    { _id: projectId },
    {
      title,
      blocks,
      audioType,
    },
  );
  return project;
};

const updateBlockRequestId = async ({ projectId, blockId, requestId }) => {
  await Project.updateOne(
    {
      _id: projectId,
      'blocks._id': blockId,
    },
    {
      $set: {
        'blocks.$.requestId': requestId,
        'blocks.$.status': BLOCK_SYNTHESIS_STATUS.IN_PROGRESS,
      },
    },
  );
};

const updateBlockAudioLink = async ({
  projectId,
  requestId,
  audioLink,
  status,
  audioExpiredAt,
}) => {
  await Project.updateOne(
    {
      _id: projectId,
      'blocks.requestId': requestId,
    },
    {
      $set: {
        'blocks.$.audioLink': audioLink,
        'blocks.$.status': status,
        ...(audioExpiredAt
          ? { 'blocks.$.audioExpiredAt': audioExpiredAt }
          : {}),
      },
    },
  );
};

const deleteProject = async (projectId) => {
  const project = await Project.findByIdAndDelete(projectId);
  return project;
};

module.exports = {
  createProject,
  findProjects,
  findProject,
  updateProject,
  deleteProject,
  updateBlockRequestId,
  updateBlockAudioLink,
};
