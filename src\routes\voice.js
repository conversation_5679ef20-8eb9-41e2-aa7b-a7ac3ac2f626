const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const voiceController = require('../controllers/voice');
const voiceCloningController = require('../controllers/voiceCloning');
const { auth, hasRole } = require('../middlewares/auth');
const { createVoiceCloningValidate } = require('../validations/voiceCloning');

/* eslint-disable prettier/prettier */
router.put('/voices/voice-cloning', auth, asyncMiddleware(voiceCloningController.updateClonedVoice));
router.post('/voices/voice-cloning', auth, createVoiceCloningValidate, asyncMiddleware(voiceCloningController.createVoiceCloningVoice));
router.get('/voices/voice-cloning', auth, asyncMiddleware(voiceCloningController.getVoiceCloningVoices));
router.post('/voices', auth, hasRole("create-voice"), asyncMiddleware(voiceController.createVoices));
router.put('/voices/:voiceId', auth, hasRole("manage-voices"), asyncMiddleware(voiceController.updateVoice));
router.get('/voices', asyncMiddleware(voiceController.getVoices));
router.get('/languages', asyncMiddleware(voiceController.getLanguages));
/* eslint-disable prettier/prettier */
module.exports = router;
