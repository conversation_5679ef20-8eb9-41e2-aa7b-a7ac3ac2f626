// const { LambdaClient, InvokeCommand } = require('@aws-sdk/client-lambda');
const fs = require('fs');
const uuid = require('uuid');
const moment = require('moment');
const cheerio = require('cheerio');
const { default: axios } = require('axios');
// const { AWS_REGION, NORMALIZE_FUNCTION_NAME } = require('../configs');
const {
  EMPHASIS_LEVEL,
  VALID_SPEED,
  REGEX,
  TTS_CORE_VERSION,
  VOICE_PROVIDER,
  SYNTHESIS_TYPE,
  VALID_CHARACTERS_LENGTH_REGEX,
  MAX_BACKGROUND_MUSIC_FILE_SIZE,
  AUDIO_TYPE,
  SME_PACKAGE_CODES,
  VALID_CHARACTERS_REGEX,
  NON_VBEE_VOICE_PROVIDERS,
} = require('../constants');
const { DEFAULT_CLIENT_PAUSE } = require('../constants/clientPause');
const code = require('../errors/code');
const CustomError = require('../errors/CustomError');
const {
  getVoiceInfoByCodes,
  getVoiceInfoByCode,
  getVoiceWithCreditFactor,
} = require('./voice');
const { decryptTags } = require('./decrypt');
const { uploadToS3 } = require('./upload');
const { getAccessToken } = require('./iam');
const { deleteFile } = require('../utils/file');
const { removeHtmlTags } = require('../utils/text');
const {
  findAdminApp,
  getActiveWalletOfUserWithCurrentVoice,
} = require('./user');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { getPackageUsageOptions } = require('./package');
const { processWalletCreditsDeduction } = require('./processCredits');

// const lambda = new LambdaClient({
//   region: AWS_REGION,
// });

// const unicodeNormalization = async (text) => {
//   const params = {
//     FunctionName: NORMALIZE_FUNCTION_NAME,
//     InvocationType: 'RequestResponse',
//     Payload: JSON.stringify({ input: text }),
//   };
//   const command = new InvokeCommand(params);
//   const response = await lambda.send(command);

//   const { statusCode, result } = JSON.parse(
//     new TextDecoder('utf-8').decode(response.Payload),
//   );
//   if (statusCode !== 200) throw new Error(`statusCode = ${statusCode}`);
//   return result;
// };

const normalizeSpeedForVbeeVoice = (text) => {
  const $ = cheerio.load(text, { xmlMode: true, decodeEntities: false });

  $('prosody').each((index, element) => {
    const value = $(element).attr('rate').trim();
    if (!value.match(REGEX.VBEE_SPEED)) {
      const rateValue = Number(value.slice(0, -1));
      let rate;

      if (rateValue > 100) {
        rate = `+${rateValue - 100}%`;
      } else {
        rate = `-${100 - rateValue}%`;
      }

      $(element).attr('rate', (i, elem) => elem.replace(value, rate));
    }
  });

  return $.xml().toString();
};

const getSynthesisType = (request) => {
  if (request.sentences?.length) return SYNTHESIS_TYPE.MULTI_VOICE;
  return SYNTHESIS_TYPE.SINGLE_VOICE;
};

const countTextLength = (text, regex) => {
  regex = regex || REGEX.MULTIPLE_TAGS;
  const normalizeText = text.trim().replace(regex, '');
  const textLength = normalizeText.length;
  return textLength;
};

const countCreditsByVoiceProvider = async ({
  voiceCode,
  text,
  ssmlRegex,
  userFeatures,
  user = {},
}) => {
  const nonVbeeCreditsFactor =
    getFeatureValue(FEATURE_KEYS.NON_VBEE_CREDITS_FACTOR, {
      ...user,
      voiceCode,
    }) || 1;

  const voice = await getVoiceInfoByCode(voiceCode, user.userId, userFeatures);
  const textLength = countTextLength(text, ssmlRegex);
  return NON_VBEE_VOICE_PROVIDERS.includes(voice?.provider)
    ? textLength * nonVbeeCreditsFactor
    : textLength;
};

const countCreditsBySentence = async ({
  voiceCode,
  text,
  ssmlRegex,
  userFeatures,
  user,
}) => {
  const { packageCode } = user || {};
  const voice = await getVoiceInfoByCode(voiceCode, user.userId, userFeatures);
  const voiceWithCreditFactor = getVoiceWithCreditFactor(voice, packageCode);
  const creditFactor = voiceWithCreditFactor?.creditFactor || 1;

  if (creditFactor) {
    const textLength = countTextLength(text, ssmlRegex);
    return textLength * creditFactor;
  }

  // If feature CREDIT_FACTOR is not enabled, use countCreditsByVoiceProvider
  return countCreditsByVoiceProvider({
    voiceCode,
    text,
    ssmlRegex,
    userFeatures,
    user,
  });
};

const countCredits = async ({ sentences, ssmlRegex, user }) => {
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user.userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });
  const sentenceCreditsPromises = sentences.map(async (sentence) =>
    countCreditsBySentence({
      voiceCode: sentence.voiceCode,
      text: sentence.text,
      ssmlRegex,
      userFeatures: studioUsageOptions.features,
      user,
    }),
  );

  const sentenceCredits = await Promise.all(sentenceCreditsPromises);
  const totalCredit = sentenceCredits.reduce((acc, credit) => acc + credit, 0);
  return Math.ceil(totalCredit);
};

const countCreditsUseInWallets = async ({
  voiceCode,
  text,
  ssmlRegex,
  userFeatures,
  user,
}) => {
  const { packageCode } = user || {};
  const voice = await getVoiceInfoByCode(voiceCode, user._id, userFeatures);
  const voiceWithCreditFactor = getVoiceWithCreditFactor(voice, packageCode);
  const creditFactor = voiceWithCreditFactor?.creditFactor || 1;

  const textLength = countTextLength(text, ssmlRegex);

  const wallets = getActiveWalletOfUserWithCurrentVoice(user, voice);

  return { wallets, credits: textLength * creditFactor };
};

const countCreditsByVoiceFactor = async ({ sentences, ssmlRegex, user }) => {
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user._id,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });

  const sentenceCreditsUsedFromWallets = sentences.map(async (sentence) =>
    countCreditsUseInWallets({
      voiceCode: sentence.voiceCode,
      text: sentence.text,
      ssmlRegex,
      userFeatures: studioUsageOptions.features,
      user,
    }),
  );

  const sentenceCredits = await Promise.all(sentenceCreditsUsedFromWallets);
  return sentenceCredits;
};

const isInvalidCredits = ({
  demo,
  maxLengthDemoInput,
  maxLengthInputText,
  oneTimeCredits,
  cycleCredits,
  topUpCredits,
  customCredits,
  credits,
  textLength,
}) => {
  const totalCredits =
    oneTimeCredits + cycleCredits + topUpCredits + customCredits;

  const isInputTextTooLong = demo
    ? textLength > maxLengthDemoInput
    : textLength > maxLengthInputText;

  const isCreditsExceeded = demo ? false : credits > totalCredits; // For demo, we don't check credits exceed total credits

  return isInputTextTooLong || isCreditsExceeded;
};

const validateCredits = ({
  demo,
  maxLengthDemoInput,
  maxLengthInputText,
  oneTimeCredits = 0,
  cycleCredits = 0,
  topUpCredits = 0,
  customCredits = 0,
  credits,
  textLength,
  blockedCredits,
}) => {
  const sourceCredits = {
    oneTimeCredits,
    cycleCredits,
    topUpCredits,
    customCredits,
  };

  if (blockedCredits?.cycleCredits) sourceCredits.cycleCredits = 0;
  if (blockedCredits?.topUpCredits) sourceCredits.topUpCredits = 0;
  if (blockedCredits?.customCredits) sourceCredits.customCredits = 0;
  if (blockedCredits?.oneTimeCredits) sourceCredits.oneTimeCredits = 0;

  const isExceedingLimits = isInvalidCredits({
    demo,
    maxLengthDemoInput,
    maxLengthInputText,
    credits,
    textLength,
    ...sourceCredits,
  });

  if (isExceedingLimits) {
    throw new CustomError(code.TEXT_TOO_LONG);
  }
};

const validateWalletCredits = ({ sentencesCreditsInfo, currWallets, demo }) => {
  const validWallets = processWalletCreditsDeduction({
    sentencesCreditsInfo,
    currWallets,
  });

  if (!validWallets && !demo) throw new CustomError(code.TEXT_TOO_LONG);
};

const validateSsmlText = (text) => {
  const $ = cheerio.load(text, { xmlMode: true, decodeEntities: false });

  // const xml = $.xml();
  let isValidXml = true;
  // if (xml !== text) return false;

  $('emphasis').each((index, element) => {
    const level = $(element).attr('level').trim();
    if (!Object.values(EMPHASIS_LEVEL).includes(level)) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  $('break').each((index, element) => {
    const time = $(element).attr('time').trim();
    const seconds = time.slice(0, -1);
    const unit = time.slice(-1);

    if (unit !== 's' || seconds < 0.1 || seconds > 60) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  $('prosody').each((index, element) => {
    const value = $(element).attr('rate').trim();
    const rate = Number(value.slice(0, -1)) / 100;

    if (rate < VALID_SPEED.MIN || rate > VALID_SPEED.MAX) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  return isValidXml;
};

const validateText = ({ text, voiceProvider, ttsCoreVersion }) => {
  let isValidText = true;

  if (voiceProvider === VOICE_PROVIDER.VBEE) {
    if (!VALID_CHARACTERS_REGEX.test(text)) return false;

    if (ttsCoreVersion !== TTS_CORE_VERSION.NEW)
      isValidText = !text.match(VALID_CHARACTERS_LENGTH_REGEX);
    else {
      const pureText = text.replace(REGEX.ADVANCE_TAG, '');
      isValidText = !pureText.match(VALID_CHARACTERS_LENGTH_REGEX);
    }

    if (!isValidText) return false;
  }

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW) {
    isValidText = validateSsmlText(text);
  }

  return isValidText;
};

const processText = async ({
  text,
  voiceProvider,
  // voiceLanguage,
  ttsCoreVersion,
  paragraphBreak = DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
  hasClientPause,
}) => {
  // const isVietnameseVoice = voiceLanguage === 'vi-VN';
  let normalizeText = text;
  // change speed for vbee voice from 150% to +50%
  if (
    ttsCoreVersion === TTS_CORE_VERSION.NEW &&
    voiceProvider === VOICE_PROVIDER.VBEE &&
    !!text.match(REGEX.ADVANCE_TAG)
  ) {
    normalizeText = normalizeSpeedForVbeeVoice(normalizeText);
  }

  if (hasClientPause && voiceProvider === VOICE_PROVIDER.VBEE) {
    const breakTag =
      ttsCoreVersion === TTS_CORE_VERSION.NEW
        ? `<break time="${paragraphBreak}s"/>`
        : `<break time=${paragraphBreak}s/>`;

    normalizeText = normalizeText.replace(REGEX.BREAK_LINE, breakTag);
  }

  return normalizeText;
};

const getValidSampleRates = async (voiceCodes = [], userId, userFeatures) => {
  if (!voiceCodes.length) return [];

  const voices = await getVoiceInfoByCodes(voiceCodes, userId, userFeatures);

  const sampleRates = voices.reduce((acc, curr) => {
    const currSampleRates = curr.sampleRates || [];
    if (!acc.length) return currSampleRates;
    return currSampleRates.filter((sampleRate) => acc.indexOf(sampleRate) >= 0);
  }, []);

  return {
    sampleRates,
    maxSampleRate: sampleRates.length ? Math.max(...sampleRates) : null,
  };
};

const getMsClientPause = ({
  paragraphBreak = DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
  sentenceBreak = DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
  majorBreak = DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
  mediumBreak = DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
}) => {
  const msParagraphBreak = paragraphBreak * 1000;
  const msSentenceBreak = sentenceBreak * 1000;
  const msMajorBreak = majorBreak * 1000;
  const msMediumBreak = mediumBreak * 1000;

  return {
    paragraphBreak: msParagraphBreak.toString(),
    sentenceBreak: msSentenceBreak.toString(),
    majorBreak: msMajorBreak.toString(),
    mediumBreak: msMediumBreak.toString(),
  };
};

const getTags = ({ tagsString, appId, requestId, sessionId, aesKey }) => {
  let tags = {};
  try {
    tags = JSON.parse(tagsString);
  } catch (error) {
    logger.error(error, {
      ctx: 'CachingRequest',
      appId,
      sessionId,
    });
  }

  if (aesKey) {
    tags = decryptTags({ encryptedTags: tags, aesKey, requestId, sessionId });
  }

  return tags;
};

const getTextFromTemplateAndTags = ({ template, tags }) => {
  const personalTags = template.match(REGEX.CACHING_PERSONAL_TAG);
  if (!personalTags) return template;

  const text = personalTags.reduce((acc, curr) => {
    const tag = curr.slice(1, -1);
    if (tags[tag]) acc = acc.replace(curr, tags[tag]);

    return acc;
  }, template);

  return text;
};

const preCheckSynthesisApiRequest = async ({
  app,
  template,
  text,
  sessionId,
  tagsString,
  ttsCoreVersion,
  requestId,
  aesKey,
}) => {
  let textLength;
  let tags = {};

  if (tagsString)
    tags = getTags({
      tagsString,
      appId: app,
      requestId,
      sessionId,
      aesKey,
    });

  const { _id: appId } = app;

  const adminUser = await findAdminApp(app);

  const { remainingCharacters = 0, bonusCharacters = 0 } =
    adminUser.apiCharacters || {};

  const limitCredits = remainingCharacters + bonusCharacters;

  const { apiPackage } = adminUser || {};

  const { packageExpiryDate, packageCode, price } = apiPackage || {};
  // Get api usage options
  const apiUsageOptions = await getPackageUsageOptions({
    userId: adminUser._id,
    packageCode,
    userUsageOptions: adminUser.apiPackage,
  });

  const { maxLengthInputText, retentionPeriod, concurrentRequest } =
    apiUsageOptions;

  if (!packageCode) throw new CustomError(code.PACKAGE_NOT_EXIST);
  const hasExpiryPackage =
    (!packageExpiryDate && !SME_PACKAGE_CODES.includes(packageCode)) ||
    moment().isAfter(packageExpiryDate, 'day');
  if (hasExpiryPackage) throw new CustomError(code.PACKAGE_EXPIRED);

  if (!text) text = getTextFromTemplateAndTags({ template, tags });
  if (ttsCoreVersion === TTS_CORE_VERSION.NEW)
    textLength = countTextLength(text, REGEX.ADVANCE_TAG);
  else textLength = countTextLength(text);

  logger.info(
    { textLength, maxLengthInputText, remainingCharacters, packageCode },
    { ctx: 'CheckTextLength' },
  );

  const hasInvalidTextLength =
    textLength > maxLengthInputText ||
    (!SME_PACKAGE_CODES.includes(packageCode) &&
      textLength > remainingCharacters + bonusCharacters);
  if (hasInvalidTextLength) throw new CustomError(code.TEXT_TOO_LONG);

  return {
    appId,
    userId: adminUser._id,
    text,
    tags,
    features: apiUsageOptions.features || [],
    price,
    packageCode,
    retentionPeriod,
    concurrentRequest,
    textLength,
    user: adminUser,
    limitCredits,
  };
};

const uploadBackgroundMusicFromLink = async (link) => {
  const extension = link.split('.').pop();
  if (!Object.values(AUDIO_TYPE).includes(extension)) {
    return { status: 0, errorCode: code.INVALID_AUDIO_TYPE };
  }

  const { data: file } = await axios.get(link, {
    responseType: 'arraybuffer',
  });
  const filePath = `background-musics/${uuid.v4()}.${extension}`;
  fs.writeFileSync(filePath, file);
  const statsFile = fs.statSync(filePath);
  if (statsFile.size > MAX_BACKGROUND_MUSIC_FILE_SIZE) {
    deleteFile(filePath);
    return { status: 0, errorCode: code.FILE_TOO_LARGE };
  }

  const accessToken = await getAccessToken();
  const uploadLink = await uploadToS3(filePath, accessToken);
  const [backgroundMusicLink] = uploadLink.split('?');

  return { status: 1, link: backgroundMusicLink };
};

const getSubtitleContent = (content) => {
  const subtitleBlocks = content.split(/\n\s*\n/);
  const parsedSubtitles = [];

  for (const subtitleBlock of subtitleBlocks) {
    /**
     * subtitleBlock: string - subtitle content
     * Example:
        48
        00:03:16,280 --> 00:03:18,907
        <i>Hello, I'm a subtitle.</i>
    */
    const lines = subtitleBlock.trim().split('\n');

    // first line is index, second line is time, third line is subtitle content
    if (lines.length >= 3) {
      const subtitleContent = removeHtmlTags(lines.slice(2).join(''));
      parsedSubtitles.push(subtitleContent);
    }
  }

  return parsedSubtitles;
};

module.exports = {
  getSynthesisType,
  countTextLength,
  countCredits,
  countCreditsByVoiceFactor,
  validateSsmlText,
  validateText,
  processText,
  getValidSampleRates,
  getMsClientPause,
  getTags,
  getTextFromTemplateAndTags,
  preCheckSynthesisApiRequest,
  uploadBackgroundMusicFromLink,
  getSubtitleContent,
  validateCredits,
  validateWalletCredits,
};
