const cheerio = require('cheerio');

const convertXMLToText = (text) => {
  const $ = cheerio.load(text, null, false);

  const convertedText = $.text();
  // Case text is not xml
  if (convertedText === text) return text;

  const convertedXMLToText = convertedText
    .trim()
    .replace(/\n\s+/g, ' ')
    .replace(/\s+(,|;|\.|\?|!)/g, (char) => char.trim());

  return convertedXMLToText;
};

module.exports = { convertXMLToText };
