require('dotenv').config();
const axios = require('axios');
const { MOE_APP_ID, MOE_API_KEY, MOE_DASHBOARD } = require('../configs');
const { MOE_EVENT } = require('../constants');

const sendEventToMoEngage = async ({ customerId, actions }) => {
  try {
    const url = `https://api-${MOE_DASHBOARD}.moengage.com/v1/event/${MOE_APP_ID}`;
    const headers = {
      'Content-Type': 'application/json',
      'MOE-APPKEY': MOE_APP_ID,
      Authorization: `Basic ${Buffer.from(
        `${MOE_APP_ID}:${MOE_API_KEY}`,
      ).toString('base64')}`,
    };
    const data = { type: 'event', customer_id: customerId, actions };
    const event = await axios.post(url, data, { headers });

    const { status } = event;
    if (status !== 200) throw new Error(`MoEngage API error: ${status}`);

    return event;
  } catch (error) {
    logger.error(error, {
      ctx: 'MoEngage',
      errorMessage: JSON.stringify(error?.response?.data),
      customerId,
      actions,
    });
    
    return null;
  }
};

const sendEventCreateRequestToMoEngage = async ({
  customerId,
  request,
  timestamp,
}) => {
  const actions = [
    {
      action: MOE_EVENT.MAKE_REQUEST,
      attributes: {
        request_type: request.type,
        voice: request.voiceCode,
        characters: request.characters,
        seconds: request.seconds,
      },
      current_time: timestamp,
      user_timezone_offset: 7 * 60 * 60,
    },
  ];

  await sendEventToMoEngage({ customerId, actions });
};

module.exports = { sendEventCreateRequestToMoEngage };
