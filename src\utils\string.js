const validateYoutubeLink = (link) => {
  // Regular expression to match YouTube video URLs
  const youtubeRegex =
    /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})(?:\S*[?&](?:amp;)?\S*)*$/;

  // Test the URL against the regex
  return youtubeRegex.test(link);
};

const validateLocalLink = (link) => {
  const extension = link.split('.').pop();
  return extension === 'mp4' || extension === 'mp3';
};

const isValidUrl = (urlString) => {
  let url;
  try {
    url = new URL(urlString);
  } catch (e) {
    return false;
  }
  return url.protocol === 'http:' || url.protocol === 'https:';
};

module.exports = {
  validateYoutubeLink,
  validateLocalLink,
  isValidUrl,
};
