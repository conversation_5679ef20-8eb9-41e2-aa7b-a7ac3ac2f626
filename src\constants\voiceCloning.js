const VOICE_GENDER = {
  MALE: 'male',
  FEMALE: 'female',
};

const VOICE_LOCALE = {
  NORTHERN: 'northern',
  CENTRAL: 'central',
  SOUTHERN: 'southern',
};

const VOICE_STATUS = {
  PRIVATE: 'private',
  PUBLIC: 'public',
  REVIEWING: 'reviewing',
};

const LANGUAGE_CODE = {
  VI: 'vi-VN',
};

const VC_SAMPLE_RATES = [8000, 16000, 22050, 24000, 32000, 44100, 48000];

const DEMO_PUBLIC_VOICE_SAMPLE_RATES = [8000, 16000, 22050];

const VC_DEFAULT_SAMPLE_RATE = 22050;

// TODO: regex always need to explained and have example
const VC_VOICE_CODE_REGEX = /[ncs]_[a-z]+_(male|female)_[a-z0-9]+_[a-z]+_vc+$/;

const DEFAULT_MAX_VOICE_CLONING = 3;

module.exports = {
  VOICE_GENDER,
  VOICE_LOCALE,
  VOICE_STATUS,
  LANGUAGE_CODE,
  VC_SAMPLE_RATES,
  DEMO_PUBLIC_VOICE_SAMPLE_RATES,
  VC_DEFAULT_SAMPLE_RATE,
  VC_VOICE_CODE_REGEX,
  DEFAULT_MAX_VOICE_CLONING,
};
