const { TEXT_DEMO_MAX_LENGTH } = require('../configs');
const codes = require('./code');

const getErrorMessage = (code) => {
  switch (code) {
    case codes.INVALID_VOICE_CODE:
      return 'Invalid voice code';
    case codes.TEXT_TOO_LONG:
      return 'Text too long';
    case codes.USER_NOT_FOUND:
      return 'User is not found';
    case codes.PACKAGE_EXPIRED:
      return 'Package has expired';
    case codes.REQUEST_NOT_FOUND:
      return 'Request is not found';
    case codes.PROJECT_NOT_FOUND:
      return 'Project is not found';
    case codes.INVALID_SYNTAX:
      return 'Invalid syntax';
    case codes.PACKAGE_NOT_EXIST:
      return 'Package is not exist';
    case codes.TTS_FAILURE:
      return 'Convert TTS failed';
    case codes.REQUEST_TIMEOUT:
      return 'Request timeout';
    case codes.UNAVAILABLE_VOICE:
      return 'Voice is unavailable';
    case codes.APP_NOT_FOUND:
      return 'App is not found';
    case codes.APP_NOT_ACTIVATED:
      return 'App is not activated';
    case codes.IS_HATE_SPEECH:
      return 'Request is a hate speech';
    case codes.BLACK_WORD_EXIST:
      return 'This black word exists';
    case codes.BLACK_WORD_NOT_EXIST:
      return 'This black word does not exist';
    case codes.SENTENCES_NOT_SUPPORT_EMPHASIS:
      return 'Convert by sentences not support emphasis';
    case codes.INVALID_AUDIO_TYPE:
      return 'Invalid audio type, only support mp3 and wav';
    case codes.FILE_TOO_LARGE:
      return 'File too large';
    case codes.TTS_CACHING_NOT_SUPPORT_THIS_VOICE:
      return 'TTS caching not support this voice';
    case codes.TEXT_DEMO_TOO_LONG:
      return `Text demo too long (max ${TEXT_DEMO_MAX_LENGTH} characters)`;
    case codes.EXCEED_MAX_PREVIEW:
      return `Exceed max preview `;
    case codes.AWS_ZONE_EXIST:
      return 'Aws zone exist';
    case codes.USER_BLOCK:
      return 'User has been locked';
    case codes.EXCEED_PREVIEW_TTS:
      return 'User has exceed preview';
    case codes.DEMO_FEATURE_UNSUPPORTED:
      return 'Demo feature unsupported';
    case codes.VOICE_NOT_SUPPORT_DUBBING:
      return 'This voice does not support dubbing feature';
    case codes.TIMEOUT:
      return 'Timeout';
    case codes.DOWNLOAD_QUOTA_EXCEEDED:
      return 'Download quota exceeded';
    case codes.DUBBING_AUDIO_TOO_LONG:
      return 'Dubbing audio too long';
    case codes.DUBBING_ONLY_SUPPORT_VIETNAMESE:
      return 'Dubbing feature only support vietnamese text';
    case codes.DUBBING_SENTENCE_TOO_LONG:
      return 'Dubbing sentence too long';
    case codes.AUDIO_URL_EXPIRED:
      return 'Audio URL has expired';
    case codes.DUBBING_DURATION_TOO_LONG:
      return 'Dubbing duration too long';
    case codes.MISSING_DUBBING_PACKAGE:
      return 'Missing dubbing package';
    case codes.DUBBING_FAILURE:
      return 'Dubbing failure';
    case codes.UPDATE_PROJECT_STATUS_FAILURE:
      return 'Update project status failure';
    case codes.VOICE_CLONING_VOICE_EXISTED:
      return 'Voice cloning voice existed';
    case codes.CALL_API_CREATE_VOICE_IN_TTS_GATE_FAILED:
      return 'Call api create voice in tts gate failed';
    case codes.DISCARDED_VOICE_CLONING:
      return 'This voice has been discarded';
    case codes.STT_INSUFFICIENT_SECONDS:
      return 'Insufficient seconds';
    case codes.SPAM_EMAIL:
      return 'Text-to-speech feature has been blocked due to unusual activity';
    case codes.EXCEED_CHARACTERS:
      return 'Request length has exceeded remaining characters';
    case codes.INACTIVE_VOICE:
      return 'Voice is inactive';
    default:
      return null;
  }
};

module.exports = getErrorMessage;
