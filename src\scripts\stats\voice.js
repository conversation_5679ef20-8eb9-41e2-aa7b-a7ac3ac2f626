require('dotenv').config();
require('../../models');

const { VOICE_PROVIDER } = require('../../constants');
const Request = require('../../models/request');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const printVoiceStatsFileCsv = (voiceStats) => {
  const fs = require('fs');
  const path = require('path');
  const filePath = path.join(__dirname, 'voice_stats.csv');
  const csvContent = Object.entries(voiceStats)
    .sort(([, countA], [, countB]) => countB - countA) // Sort by count in descending order
    .map(([code, count]) => `${code},${count}`)
    .join('\n');
  fs.writeFileSync(filePath, csvContent);
};

const scanAndHandleByBatch = async ({
  dateField,
  dateRange,
  batchSize = 1000,
  getQueryFn,
  countDocumentsFn,
  getDocumentsFn,
  handleDocumentsFn,
  googleVoiceCodes,
}) => {
  logger.info(`Start scanning and handling documents by batch`);

  const { start, end } = dateRange;
  const query = getQueryFn({ dateField, dateRange });
  const totalDocuments = await countDocumentsFn(query);
  logger.info(`Total ${totalDocuments} documents`);

  let currTimeStamp = end;
  let handledDocs = 0;
  let handledDocIds = [];

  do {
    const q = getQueryFn({
      dateField,
      dateRange: { start, end: currTimeStamp },
      excludeDocIds: handledDocIds,
    });
    const documents = await getDocumentsFn(q, {
      limit: batchSize,
      sortField: dateField,
    });
    if (!documents.length) break;

    // Handle documents
    await handleDocumentsFn(documents, googleVoiceCodes);

    // Update progress
    currTimeStamp = documents[documents.length - 1][dateField];
    handledDocs += documents.length;
    handledDocIds = documents.map((doc) => doc._id);
    const percent = ((handledDocs / totalDocuments) * 100).toFixed(2);
    logger.debug(
      `Progress: ${percent}% - docs: ${handledDocs} - currTimestamp: ${currTimeStamp}`,
    );

    printVoiceStatsFileCsv(VOICES);

    if (handledDocs >= totalDocuments) break;
  } while (true);

  logger.info('Completed scanning and handling documents by batch');
};

const getRequestsQuery = ({ dateRange, dateField, excludeDocIds = [] }) => {
  const { start, end } = dateRange;
  return {
    _id: { $nin: excludeDocIds },
    [dateField]: { $gte: start, $lte: end },
  };
};

const countRequests = async (condition) => {
  const totalUsers = await Request.countDocuments(condition);
  return totalUsers;
};

const getRequests = async (query, { offset = 0, limit, sortField }) => {
  const requests = await Request.find(query)
    .select({ voiceCode: 1, createdAt: 1 })
    .sort({ [sortField]: -1 })
    .skip(offset)
    .limit(limit);
  return requests;
};

const VOICES = {};

const handleVoiceStats = (requests, googleVoiceCodes) => {
  requests.forEach((request) => {
    if (googleVoiceCodes.includes(request.voiceCode)) {
      VOICES[request.voiceCode] = VOICES[request.voiceCode]
        ? VOICES[request.voiceCode] + 1
        : 1;
    }
  });
};

(async () => {
  const googleVoices = await Voice.find({
    provider: VOICE_PROVIDER.GOOGLE,
  }).select('code provider');
  const googleVoiceCodes = googleVoices.map((voice) => voice.code);

  const startDate = '2025-02-13T00:00:00.000+07:00';
  const endDate = '2025-03-14T00:00:00.000+07:00';
  const BATCH_SIZE = 1000;

  await scanAndHandleByBatch({
    dateField: 'createdAt',
    dateRange: {
      start: startDate,
      end: endDate,
    },
    batchSize: BATCH_SIZE,
    getQueryFn: getRequestsQuery,
    countDocumentsFn: countRequests,
    getDocumentsFn: getRequests,
    handleDocumentsFn: (requests) =>
      handleVoiceStats(requests, googleVoiceCodes),
  });
})();
