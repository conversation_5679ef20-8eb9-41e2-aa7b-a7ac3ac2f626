const getIp = (headers) => {
  if (!headers) return null;

  const ip =
    headers['cloudfront-viewer-address'] || // CloudFront
    headers['x-original-forwarded-for'] || // Nginx Ingress
    headers['x-forwarded-for'] ||
    headers['x-real-ip'];

  if (!ip) return null;

  // Example: ******* or *******:8080
  const isIPv4 = ip.includes('.');
  if (isIPv4) return ip.split(':')[0];

  // Example: [2406:da18:d9c:5d00:cde7:2cba:aef2:49c9]:46568
  const isIPv6WithPort = ip.includes(']:');
  if (isIPv6WithPort) return ip.split(']:')[0].slice(1);

  // Example: 2406:da18:d9c:5d00:cde7:2cba:aef2:49c9:46568
  const colonCount = ip.match(/:/g).length;
  if (colonCount === 8) {
    const lastColonIndex = ip.lastIndexOf(':');
    return ip.slice(0, lastColonIndex);
  }

  // There are stills some ipv6 cases that are not handled
  // (1) Compressing consecutive zero blocks: 2001:db8:85a3::8a2e:370:7334
  // (2) IPv6 Address with Port: [2001:db8:85a3::8a2e:370:7334]:443
  // (3) Loopback Address: ::1
  // (4) Link-local Address: fe80::1ff:fe23:4567:890a
  // And many more...

  return ip;
};

module.exports = { getIp };
