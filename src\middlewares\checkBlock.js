const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const userDao = require('../daos/user');
const asyncMiddleware = require('./async');

const checkBlock = async (req, res, next) => {
  const { userId } = req.user;
  const user = await userDao.findUser({ _id: userId });
  if (user?.isBlock) throw new CustomError(errorCodes.USER_BLOCK);

  return next();
};

const checkBlockApi = async (req, res, next) => {
  const { members } = req.apiApp;
  if (!members) throw new CustomError(errorCodes.NOT_FOUND);

  const { userId: user } = members[0];
  if (!user) throw new CustomError(errorCodes.NOT_FOUND);

  const userDB = await userDao.findUser({ _id: user._id });
  const { isBlock } = userDB || {};
  if (isBlock) throw new CustomError(errorCodes.USER_BLOCK);

  return next();
};
module.exports = {
  checkBlock: asyncMiddleware(checkBlock),
  checkBlockApi: asyncMiddleware(checkBlockApi),
};
