const { AUDIO_MODE } = require('../constants');
const projectService = require('../services/project');

const createProject = async (req, res) => {
  const { userId } = req.user;
  const { title, product, blocks } = req.body;
  const project = await projectService.createProject({
    userId,
    title,
    product,
    blocks,
  });

  return res.send({ project });
};

const synthesisProject = async (req, res) => {
  const { userId, email } = req.user;
  const { projectId, blocks } = req.body;
  const synthesisProjectResult = await projectService.synthesisProject({
    userId,
    email,
    projectId,
    blocks,
  });
  return res.send(synthesisProjectResult);
};

const getProjects = async (req, res) => {
  const { userId } = req.user;
  const { limit, offset, sort, search } = req.query;
  const { projects, total } = await projectService.getProjects({
    userId,
    limit: parseInt(limit, 10),
    offset: parseInt(offset, 10),
    sort: sort.split(','),
    search,
  });

  return res.send({ projects, metadata: { total } });
};

const getProject = async (req, res) => {
  const { userId } = req.user;
  const { projectId } = req.params;
  const project = await projectService.getProject({ userId, projectId });

  return res.send({ project });
};

const updateProject = async (req, res) => {
  const { userId } = req.user;
  const { projectId } = req.params;
  const { title, blocks, audioType } = req.body;
  const project = await projectService.updateProject({
    userId,
    projectId,
    title,
    blocks,
    audioType,
  });

  return res.send({ project });
};

const deleteProject = async (req, res) => {
  const { userId } = req.user;
  const { projectId } = req.params;
  const project = await projectService.deleteProject({ userId, projectId });
  return res.send({ project });
};

const getAudioLink = async (req, res) => {
  const { userId } = req.user;
  const { projectId } = req.params;
  const { mode } = req.query;
  let audioLink;

  switch (mode) {
    case AUDIO_MODE.JOINED:
      audioLink = await projectService.getJoinedAudio({
        userId,
        projectId,
      });
      break;
    case AUDIO_MODE.SEPARATE:
      // TODO: Implement getSeparateAudio
      break;
    default:
      logger.warn(`Unsupported download audio mode ${mode}`);
  }
  return res.send({ audioLink });
};

const getSampleProject = async (_, res) => {
  const sampleProject = await projectService.getSampleProject();
  return res.send({ sampleProject });
};

module.exports = {
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  synthesisProject,
  getAudioLink,
  getSampleProject,
};
