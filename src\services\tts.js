const { SYNTHESIS_BY_GATEWAY, TTS_GATE_URL } = require('../configs');
const { findTts } = require('../daos/tts');
// const { convertXMLToText } = require('../utils/xml');
const callApi = require('../utils/callApi');

const getTts = async ({
  search,
  searchFields,
  dateField,
  query,
  offset,
  limit,
  fields,
  sort,
  requestId,
}) => {
  let res;

  if (SYNTHESIS_BY_GATEWAY) {
    const ttsRes = await callApi({
      url: `${TTS_GATE_URL}/api/v1/tts`,
      method: 'GET',
      params: {
        search,
        searchFields,
        dateField,
        query,
        offset,
        limit,
        fields,
        sort,
        requestId,
      },
    });
    res = ttsRes.result || {};
  } else
    res = await findTts({
      search,
      searchFields,
      dateField,
      query,
      offset,
      limit,
      fields,
      sort,
    });

  const { tts, total } = res;
  if (!tts?.length) return { tts: [], total: 0 };

  return { tts, total };
};

module.exports = { getTts };
