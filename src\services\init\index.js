const uuid = require('uuid');
const { mkDirByPathSync } = require('../../utils/file');
const logger = require('../../utils/logger');
const { SERVICE } = require('../../configs');
const { FEATURE_KEYS } = require('../../constants/featureKeys');
const { loadFeaturesRealtime, getFeatureValue } = require('../growthbook');
const { syncBannedAccountsToCache } = require('../bannedAccount');
const { initPackages } = require('../package');

mkDirByPathSync('background-musics');

// ADVISE: avoid using global (it hurts intellisense, code analysis tools, ...) if possible (should  import/export from a singleton module).

global.KAFKA_CONSUMER_GROUP_RANDOM_TTS = uuid.v4();
global.VOICES = null;
global.LANGUAGES = null;
global.SAMPLE_SCRIPTS = null;
// ADVISE: duplicated code REQUEST_DIRECT?
global.REQUEST_DIRECT = {};
global.REQUEST_DIRECT = {};
global.AWS_ZONES_TTS_CACHING = {};
global.AWS_ZONES_TTS_STUDIO = {};
global.AWS_S3_ACCESS = {};
global.logger = logger;

// TODO: getFeatureValue too early? before growthbook ready
global.logLevel = () =>
  getFeatureValue(FEATURE_KEYS.LOG_LEVEL, { service: SERVICE });
global.BANNED_ACCOUNTS = [];

// ADVISE: we should import the init func from other modules, and call it here. require() and execute() in the same file is not a good practice (because we don't know if a require() execute something ot not).
require('./iam');
require('../ws');
require('./voices');
require('./languages');
require('./blackWords');
require('./apps');
require('./sampleScripts');

loadFeaturesRealtime().then(() => {
  require('./awsZone');
  require('../kafka');
  require('../healthCheck/downstream').runCheckDownstreamHealthy();
  initPackages();
});

syncBannedAccountsToCache();

// require('./backgroundMusic');
