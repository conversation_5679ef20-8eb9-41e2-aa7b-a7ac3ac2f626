const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const asyncMiddleware = require('./async');
const { containsBlacklistedWords } = require('../services/blackWord');

const blockBlackWords = async (req, res, next) => {
  // TODO: we need req.body || {}
  // TODO: what if we don't have any of [inputText, sentences, text, userId, email, phoneNumber]? throw CustomError or return silently?
  const { inputText, sentences, text } = req.body;

  // Get text
  const SPACE = ' ';
  const sentencesText = sentences?.reduce(
    (prev, curr) => prev + SPACE + (curr.text || curr.inputText)?.trim(),
    '',
  );
  const requestText = inputText || text || sentencesText;

  // Check if the request text contains blacklisted words
  const isHateSpeech = containsBlacklistedWords(requestText);
  if (isHateSpeech) throw new CustomError(errorCodes.IS_HATE_SPEECH);

  next();
};

module.exports = {
  blockBlackWords: asyncMiddleware(blockBlackWords),
};
